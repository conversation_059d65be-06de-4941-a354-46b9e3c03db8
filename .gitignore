# Created by https://www.toptal.com/developers/gitignore/api/node,nextjs,react,macos,vercel,python,fastapi
# Edit at https://www.toptal.com/developers/gitignore?templates=node,nextjs,react,macos,vercel,python,fastapi
# Last updated: 2025-01-15
#
# Cube1 Group Monorepo - Complete Technology Stack
# Frontend: Next.js + React + TypeScript + Tailwind + Prisma + Storybook
# Backend: FastAPI + Python + SQLModel + Alembic + Poetry
# Tools: Turbo + pnpm + Docker + Vercel

# ===================================================================
# macOS
# ===================================================================
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# iCloud generated files
*.icloud

# ===================================================================
# Node.js
# ===================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
**/node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local
.env*.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Serverless Webpack directories
.webpack/

# SvelteKit build / generate output
.svelte-kit

# ===================================================================
# Next.js
# ===================================================================
# dependencies
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
*.pem

# typescript
*.tsbuildinfo
next-env.d.ts

# ===================================================================
# React
# ===================================================================
**/*.backup.*
**/*.back.*

*.sublime*

psd
thumb
sketch

# ===================================================================
# Vercel
# ===================================================================
.vercel

# ===================================================================
# Python & FastAPI
# ===================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
# --- MODIFICATION START ---
# Add exception for frontend lib directory, which is not a Python build artifact
!apps/frontend/lib/
# --- MODIFICATION END ---
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Ruff cache
.ruff_cache/

# ===================================================================
# Poetry (Python Package Manager)
# ===================================================================
# Note: poetry.lock should be committed to version control
# .venv/ is handled above in Python section

# ===================================================================
# Docker
# ===================================================================
# Docker override files
docker-compose.override.yml
docker-compose.override.yaml

# Docker volumes
.docker/

# ===================================================================
# Turbo (Monorepo Build System)
# ===================================================================
.turbo/

# ===================================================================
# Storybook
# ===================================================================
storybook-static/
**/storybook-static/

# ===================================================================
# Playwright (E2E Testing)
# ===================================================================
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# ===================================================================
# Database Files
# ===================================================================
# SQLite databases
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-shm
*.db-wal

# Development databases
cube1_dev.db
dev.db
test.db
**/cube1_dev.db
**/dev.db
**/test.db

# Database backups
*.sql.backup
*.dump

# Note: Keep schema files and migration files in version control
# prisma/schema.prisma - KEEP
# alembic/versions/*.py - KEEP

# ===================================================================
# Project Specific
# ===================================================================
# Prisma
/lib/generated/prisma
prisma/dev.db*
prisma/migrations/migration_lock.toml

# Test reports and coverage
test-report.json
/coverage/
jest-coverage/
.nyc_output/
coverage/
htmlcov/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# YoYo AI version control directory
.yoyo/

# Build artifacts
dist/
build/

# Temporary files
*.tmp
*.temp
.temp/
.cache/

# OS generated files
Thumbs.db
ehthumbs.db

# Application logs
logs/
*.log
app.log
error.log
debug.log

# Environment and configuration
.env*
!.env.example
config.local.json

# Performance and monitoring
lighthouse-report.html
bundle-analyzer-report.html

# Backup files
*.backup
*.bak
*.orig

# Archive files
*.zip
*.tar.gz
*.rar

# Lock files (keep package manager lock files)
# package-lock.json - KEEP if using npm
# yarn.lock - KEEP if using yarn
# pnpm-lock.yaml - KEEP (already in repo)
# poetry.lock - KEEP (already in repo)

# ===================================================================
# Apps Directory Specific Rules
# ===================================================================
# Backend specific
apps/backend/**/*.db
apps/backend/**/*.sqlite*
apps/backend/logs/
apps/backend/.env*
apps/backend/__pycache__/
apps/backend/**/__pycache__/
apps/backend/.pytest_cache/
apps/backend/htmlcov/
apps/backend/.coverage
apps/backend/instance/

# Frontend specific
apps/frontend/node_modules/
apps/frontend/.next/
apps/frontend/out/
apps/frontend/build/
apps/frontend/dist/
apps/frontend/storybook-static/
apps/frontend/.env*
apps/frontend/*.tsbuildinfo
apps/frontend/coverage/
apps/frontend/test-results/
apps/frontend/playwright-report/
apps/frontend/blob-report/
apps/frontend/.turbo/
apps/frontend/prisma/*.db*
apps/frontend/prisma/dev.db*