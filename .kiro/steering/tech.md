---
inclusion: manual
---

# 技术栈与开发指南

## 核心技术

### 前端技术栈

- **框架**: Next.js 15.1.0 (App Router) - 默认使用服务器组件
- **UI**: React 18.3.1 (仅函数组件) + Tailwind CSS + Radix UI
- **语言**: TypeScript 5.8.3 (启用严格模式)
- **状态管理**: Zustand 5.0.6 配合持久化中间件处理关键数据
- **数据**: @tanstack/react-query 5.83.0 用于API调用，Prisma 6.11.1 用于数据库
- **测试**: Vitest 3.2.4 (单元测试)，Playwright 1.54.0 (端到端测试)
- **工具**: Turbo 2.3.0 (Monorepo构建)，ESLint，Prettier

### 后端技术栈

- **框架**: FastAPI 0.116.1 配合 SQLModel 0.0.24
- **语言**: Python 3.11+ 使用 Poetry 依赖管理
- **数据库**: SQLite (开发环境) / PostgreSQL (生产环境)
- **工具**: Ruff (代码检查)，Black (代码格式化)，MyPy (类型检查)

## 架构模式

### 组件设计

- 所有组件使用 React.memo 防止不必要的重新渲染
- 将展示组件与业务逻辑分离
- 复杂UI遵循复合组件模式
- 关键部分实现错误边界

### 状态管理

- 使用 Zustand 存储配合 TypeScript 严格类型
- 存储分离：basicDataStore (网格数据、矩阵数据管理)、styleStore (UI配置、样式预设管理)、dynamicStyleStore (计算样式、响应式样式、交互状态)、gridConfigStore (网格配置、显示模式、灰色模式管理)
- 为用户偏好和网格状态实现持久化中间件，支持版本迁移
- 避免属性钻取 - 使用存储进行跨组件通信
- 支持自定义存储适配器处理复杂数据结构（如Map对象）

### 性能要求

- 网格必须渲染 1089 个单元格 (33x33) 且不使用虚拟化
- 对昂贵计算使用 useMemo/useCallback
- 用户交互实现防抖 (最少 100ms)
- 批量状态更新防止渲染抖动

## 代码约定

### 命名标准

#### 前端命名规范

- **组件**: PascalCase (GridContainer.tsx, ControlPanelProvider.tsx)
- **钩子**: camelCase 带 'use' 前缀 (useCellDataManager.ts, useValidation.ts)
- **存储**: camelCase 带 'Store' 后缀 (basicDataStore.ts, styleStore.ts)
- **类型/接口**: PascalCase (CellData, GridConfig, ApiResponse)
- **枚举**: PascalCase (DisplayMode, ColorCategory)
- **常量**: SCREAMING_SNAKE_CASE (MAX_GRID_SIZE, DEFAULT_CELL_COLOR)
- **服务类**: PascalCase 带 'Service' 后缀 (ColorMappingService.ts)
- **工具函数**: camelCase (cellDataHelpers.ts, matrixUtils.ts)
- **API路由**: kebab-case (/api/grid-data, /api/health-check)

#### 后端命名规范 (Python)

- **模块/文件**: snake_case (grid_data.py, project_settings.py)
- **类**: PascalCase (GridData, ProjectSettings, BaseModel)
- **函数/方法**: snake_case (get_grid_data, create_project)
- **变量**: snake_case (cell_data, project_id, user_settings)
- **常量**: SCREAMING_SNAKE_CASE (MAX_GRID_SIZE, DEFAULT_DATABASE_URL)
- **私有方法**: 下划线前缀 (_validate_data, _process_grid)
- **API端点**: kebab-case (/api/v1/grid-data, /api/v1/projects)

#### 目录结构命名

- **功能目录**: kebab-case (grid-system, style-management)
- **技术目录**: camelCase (components, lib, stores)
- **配置目录**: kebab-case (docs, scripts, tests)
- **资源目录**: kebab-case (public, assets, images)

#### 数据库命名

- **表名**: snake_case (grid_data, project_settings, black_cell_data)
- **字段名**: snake_case (created_at, updated_at, cell_value)
- **索引名**: snake_case 带前缀 (idx_grid_data_project_id)
- **约束名**: snake_case 带前缀 (fk_grid_data_project, uk_project_name)

#### Git 分支命名

- **功能分支**: feature/kebab-case (feature/grid-optimization)
- **修复分支**: fix/kebab-case (fix/cell-rendering-bug)
- **发布分支**: release/version (release/v1.2.0)
- **热修复**: hotfix/kebab-case (hotfix/critical-data-loss)

#### 命名规范工具

- **检查脚本**: `pnpm run naming:check` - 检查命名规范合规性
- **修复脚本**: `pnpm run naming:fix` - 自动修正命名问题
- **ESLint规则**: 自动检查代码中的命名约定
- **Git Hooks**: 提交前自动验证命名规范

#### 环境变量命名

- **前端**: NEXT_PUBLIC_* (NEXT_PUBLIC_API_URL)
- **后端**: SCREAMING_SNAKE_CASE (DATABASE_URL, SECRET_KEY)
- **开发**: 使用 .env.local 和 .env.example

### TypeScript 规则

- 所有 tsconfig 文件启用严格模式
- 尽可能使用仅类型导入
- 为所有组件属性定义接口
- 为API响应实现适当的错误类型

### 数据处理

- 网格坐标使用基于0的索引 (33x33网格为0-32)
- 根据定义的模式验证所有用户输入
- 实现带回滚功能的乐观更新
- 使用描述性变量名 (cellIndex, colorCategory, dataLevel)

## 开发命令

### Monorepo 命令

```bash
pnpm install              # 安装所有依赖
pnpm run dev              # 启动所有应用
pnpm run build            # 构建所有应用
pnpm run lint             # 检查所有应用代码
pnpm run test             # 运行所有测试
pnpm run clean            # 清理构建缓存
```

### 前端命令 (apps/frontend)

```bash
pnpm run dev              # 开发服务器 (localhost:4096)
pnpm run type-check       # TypeScript 验证
pnpm run db:studio        # Prisma Studio
pnpm run test             # 运行单元测试
pnpm run test:e2e         # 运行端到端测试
pnpm run naming:check     # 检查命名规范
pnpm run naming:fix       # 修复命名问题
```

### 后端命令 (apps/backend)

```bash
poetry install            # 安装 Python 依赖
poetry run uvicorn app.main:app --reload  # 开发服务器 (localhost:8000)
```

## 质量标准

### 错误处理

- 在 React 组件中实现适当的错误边界
- 对异步操作使用 try-catch 块
- 为用户提供有意义的错误消息
- 记录错误时提供足够的调试上下文

### 测试要求

- 测试满载单元格的网格渲染性能
- 验证数据持久化和同步
- 为关键UI实现视觉回归测试
- 使用 Playwright 进行端到端测试，Vitest 进行单元测试
