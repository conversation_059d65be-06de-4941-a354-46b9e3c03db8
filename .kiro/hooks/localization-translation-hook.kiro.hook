{"enabled": true, "name": "Localization Translation Monitor", "description": "Monitor changes to files containing user-facing text content (such as .json, .yaml, or other localization files) in the primary language. When changes are detected, automatically identify the new or modified text and generate translations for all configured target languages. Ensure translations maintain proper context and meaning while adhering to locale-specific conventions.", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.json", "**/*.yaml", "**/*.yml", "**/locales/**/*", "**/i18n/**/*", "**/translations/**/*", "**/lang/**/*"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes and:\n\n1. Identify any new or modified text content that requires translation\n2. Determine the primary language of the modified content\n3. Generate accurate translations for all configured target languages\n4. Ensure translations maintain proper context, tone, and cultural appropriateness\n5. Follow locale-specific formatting conventions (date formats, number formats, etc.)\n6. Preserve any interpolation variables, HTML tags, or special formatting\n7. Update or create the corresponding translation files for each target language\n8. Validate that all translation keys are consistent across language files\n\nFocus on maintaining translation quality while ensuring technical accuracy and cultural sensitivity."}}