# 设计文档

## 概述

选择性颜色显示功能为现有的33x33网格系统添加了一个新的显示模式，允许用户在灰度基准视图和选择性颜色激活之间切换。该功能通过扩展现有的显示模式系统和增强A组控制面板来实现，提供直观的颜色选择控制，同时保持最佳性能。

## 架构

### 核心组件架构

```
选择性颜色显示系统
├── 显示模式扩展
│   ├── GridConfig 类型扩展 (gray 模式)
│   ├── GridCell 渲染逻辑更新
│   └── GridMatrix 显示模式处理
├── 状态管理
│   ├── BasicDataStore 扩展 (灰度模式状态)
│   ├── 颜色激活状态管理
│   └── 会话状态持久化
├── 控制面板集成
│   ├── GroupAPanel 增强
│   ├── 颜色按钮交互逻辑
│   └── 视觉反馈系统
└── 性能优化
    ├── 高效的颜色状态管理
    ├── 防抖交互处理
    └── 选择性重新渲染
```

### 数据流架构

```
用户交互 → 控制面板 → 状态更新 → 网格重新渲染
    ↓           ↓           ↓           ↓
灰度切换    颜色激活    状态管理    视觉更新
    ↓           ↓           ↓           ↓
显示模式    激活集合    Zustand     React.memo
```

## 组件和接口

### 1. 类型定义扩展

```typescript
// 扩展现有的 DisplayMode 类型
export type DisplayMode = 'value' | 'coordinates' | 'color' | 'gray';

// 新增灰度模式配置接口
export interface GrayModeConfig {
  isActive: boolean;
  activeColors: Set<string>;
  previousDisplayMode: DisplayMode;
}

// 扩展 GridConfig 接口
export interface GridConfig {
  // ... 现有属性
  displayMode: DisplayMode;
  grayMode?: GrayModeConfig;
}

// 颜色激活状态接口
export interface ColorActivationState {
  [colorName: string]: boolean;
}
```

### 2. BasicDataStore 扩展

```typescript
// 扩展现有的 BasicDataStore
export interface BasicDataStoreState {
  // ... 现有状态
  
  // 灰度模式状态
  grayModeActive: boolean;
  grayModeActiveColors: Set<string>;
  previousDisplayMode: DisplayMode | null;
  
  // 操作方法
  toggleGrayMode: () => void;
  toggleGrayModeActiveColor: (color: string) => void;
  clearGrayModeActiveColors: () => void;
  setGrayModeActiveColors: (colors: Set<string>) => void;
}
```

### 3. GridCell 组件增强

```typescript
// GridCell 组件的颜色渲染逻辑
const getBackgroundColor = (
  cell: CellData,
  config: GridConfig,
  isActive: boolean,
  grayModeActiveColors: Set<string>
): string => {
  // 灰度模式逻辑
  if (config.displayMode === 'gray') {
    const cellColor = getCellColorByCoordinate(cell.x, cell.y);
    
    if (cellColor && grayModeActiveColors.has(cellColor)) {
      // 显示原始颜色
      return getOriginalCellColor(cellColor);
    }
    
    // 显示灰色
    return config.theme === 'minimal' ? '#f3f4f6' : '#e5e7eb';
  }
  
  // 其他显示模式的现有逻辑
  return getStandardBackgroundColor(cell, config, isActive);
};
```

### 4. GroupAPanel 组件增强

```typescript
// 颜色按钮交互逻辑
const handleColorToggle = (color: string) => {
  if (config.displayMode === 'gray') {
    // 灰度模式：切换颜色激活状态
    toggleGrayModeActiveColor(color);
  } else {
    // 其他模式：现有的可见性控制逻辑
    handleExistingColorToggle(color);
  }
};

// 视觉反馈渲染
const getColorButtonStyle = (color: string) => {
  if (config.displayMode === 'gray') {
    return grayModeActiveColors.has(color)
      ? 'bg-blue-100 border-blue-300 ring-2 ring-blue-200'
      : 'bg-gray-100 border-gray-200 opacity-50';
  }
  
  return getStandardButtonStyle(color);
};
```

## 数据模型

### 1. 颜色映射数据结构

```typescript
// 利用现有的 OPTIMIZED_GROUP_A_DATA
const COLOR_COORDINATE_MAP = new Map<string, string>();

// 初始化颜色坐标映射
const initializeColorCoordinateMap = () => {
  Object.entries(OPTIMIZED_GROUP_A_DATA).forEach(([color, levels]) => {
    Object.values(levels).forEach(coordinates => {
      coordinates.forEach(([x, y]) => {
        COLOR_COORDINATE_MAP.set(`${x},${y}`, color);
      });
    });
  });
};
```

### 2. 状态持久化模型

```typescript
// 会话状态结构
interface SelectiveColorDisplaySession {
  grayModeActive: boolean;
  activeColors: string[];
  previousDisplayMode: DisplayMode;
  timestamp: number;
}

// 存储键
const SESSION_STORAGE_KEY = 'selective-color-display-session';
```

## 错误处理

### 1. 数据验证

```typescript
// 颜色激活状态验证
const validateColorActivationState = (colors: Set<string>): boolean => {
  const validColors = Object.keys(OPTIMIZED_GROUP_A_DATA);
  return Array.from(colors).every(color => validColors.includes(color));
};

// 显示模式验证
const validateDisplayModeTransition = (
  from: DisplayMode,
  to: DisplayMode
): boolean => {
  const validTransitions = {
    'value': ['gray', 'coordinates', 'color'],
    'coordinates': ['gray', 'value', 'color'],
    'color': ['gray', 'value', 'coordinates'],
    'gray': ['value', 'coordinates', 'color']
  };
  
  return validTransitions[from]?.includes(to) ?? false;
};
```

### 2. 错误恢复策略

```typescript
// 灰度模式错误恢复
const recoverFromGrayModeError = (error: Error) => {
  console.warn('Gray mode error, recovering:', error.message);
  
  // 清除激活状态
  clearGrayModeActiveColors();
  
  // 恢复到之前的显示模式
  const previousMode = getPreviousDisplayMode();
  if (previousMode && previousMode !== 'gray') {
    setDisplayMode(previousMode);
  } else {
    setDisplayMode('value'); // 默认模式
  }
};
```

## 测试策略

### 1. 单元测试

```typescript
// 颜色激活逻辑测试
describe('Selective Color Display', () => {
  test('should activate single color in gray mode', () => {
    const store = createTestStore();
    store.toggleGrayMode();
    store.toggleGrayModeActiveColor('red');
    
    expect(store.grayModeActiveColors.has('red')).toBe(true);
    expect(store.grayModeActiveColors.size).toBe(1);
  });
  
  test('should handle multiple color activation', () => {
    const store = createTestStore();
    store.toggleGrayMode();
    store.toggleGrayModeActiveColor('red');
    store.toggleGrayModeActiveColor('blue');
    
    expect(store.grayModeActiveColors.size).toBe(2);
  });
});
```

### 2. 集成测试

```typescript
// 端到端灰度模式测试
describe('Gray Mode Integration', () => {
  test('should render gray cells with selective color activation', () => {
    render(<GridMatrix config={{ displayMode: 'gray' }} />);
    
    // 验证所有格子初始为灰色
    const cells = screen.getAllByRole('gridcell');
    cells.forEach(cell => {
      expect(cell).toHaveStyle('background-color: #e5e7eb');
    });
    
    // 激活红色
    fireEvent.click(screen.getByText('red'));
    
    // 验证红色格子显示原色
    const redCells = screen.getAllByTestId(/grid-cell.*red/);
    redCells.forEach(cell => {
      expect(cell).toHaveStyle('background-color: #ef4444');
    });
  });
});
```

### 3. 性能测试

```typescript
// 性能基准测试
describe('Performance Tests', () => {
  test('should complete gray mode transition within 200ms', async () => {
    const startTime = performance.now();
    
    const store = createTestStore();
    store.toggleGrayMode();
    
    await waitFor(() => {
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(200);
    });
  });
  
  test('should handle rapid color toggles without performance degradation', () => {
    const store = createTestStore();
    store.toggleGrayMode();
    
    const startTime = performance.now();
    
    // 快速切换多个颜色
    for (let i = 0; i < 10; i++) {
      store.toggleGrayModeActiveColor('red');
      store.toggleGrayModeActiveColor('blue');
    }
    
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100);
  });
});
```

## 实现细节

### 1. 状态管理优化

- 使用 `Set` 数据结构存储激活的颜色，提供 O(1) 查找性能
- 实现防抖机制，避免快速切换时的性能问题
- 使用 React.memo 和 useMemo 优化组件重新渲染

### 2. 渲染优化

- 在 GridCell 组件中实现高效的颜色计算逻辑
- 使用 CSS 变量和类名切换而不是内联样式
- 实现选择性重新渲染，只更新受影响的格子

### 3. 用户体验增强

- 提供平滑的过渡动画（200ms 内完成）
- 实现直观的视觉反馈系统
- 添加键盘导航支持
- 提供无障碍访问功能

### 4. 错误处理和恢复

- 实现全面的错误边界处理
- 提供自动恢复机制
- 记录详细的错误日志用于调试
- 实现优雅的降级策略

这个设计确保了选择性颜色显示功能与现有系统的无缝集成，同时保持了高性能和良好的用户体验。