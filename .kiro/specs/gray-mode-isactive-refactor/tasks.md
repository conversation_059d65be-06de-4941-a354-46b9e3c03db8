# 实施计划

- [ ] 1. 更新类型定义和接口
  - 从DisplayMode类型中移除'gray'并创建BaseDisplayMode类型
  - 为新的灰色模式配置创建GrayModeConfig接口
  - 更新GridConfig接口以使用BaseDisplayMode并包含grayMode属性
  - _需求: 5.1, 5.2, 5.3_

- [ ] 2. 更新默认配置和常量
  - 创建DEFAULT_GRAY_MODE_CONFIG，使用正确的灰色（不是黑色）
  - 更新DEFAULT_GRID_CONFIG以使用新结构
  - 添加向后兼容的配置迁移函数
  - _需求: 5.1, 5.2, 6.1, 6.2, 6.3_

- [ ] 3. 重构GridCell组件背景颜色逻辑
  - 移除第256行的错误黑色逻辑
  - 使用isActive和灰色模式配置实现新的getCellBackgroundColor函数
  - 确保非激活单元格显示正确的灰色，而不是黑色
  - 为性能添加适当的记忆化
  - _需求: 1.1, 1.2, 1.3, 1.4, 5.1, 5.4_

- [ ] 4. 重构GridCell组件内容显示逻辑
  - 更新getCellContent函数以处理灰色模式与基础显示模式
  - 实现坐标模式的正确逻辑（不受isActive影响）
  - 实现数值模式的正确逻辑（仅在isActive为true时显示）
  - 实现颜色模式的正确逻辑（无文字覆盖）
  - _需求: 2.1, 2.2, 2.3, 3.1, 3.2, 4.1, 4.2, 4.3_

- [ ] 5. 更新DisplayModeControl组件
  - 从单选按钮选项中移除灰色模式
  - 为灰色模式添加单独的切换/复选框
  - 更新组件属性以处理baseDisplayMode和grayModeEnabled
  - 实现模式切换的正确事件处理器
  - _需求: 6.1, 6.2, 6.3, 6.4, 7.1, 7.2_

- [ ] 6. 更新StylePanel组件
  - 从显示模式选择中移除灰色模式
  - 在面板中添加灰色模式切换控件
  - 更新状态管理集成
  - 确保灰色模式状态的正确UI反馈
  - _需求: 6.1, 6.2, 6.3, 7.1, 7.3_

- [ ] 7. 为新配置结构更新Zustand存储
  - 更新basicDataStore以处理新的灰色模式配置
  - 更新styleStore以分离基础显示模式和灰色模式
  - 添加独立切换灰色模式的操作
  - 为新结构实现正确的状态持久化
  - _需求: 5.3, 7.1, 7.2, 7.3, 7.4_

- [ ] 8. 添加配置验证和迁移逻辑
  - 实现BaseDisplayMode值的验证
  - 为现有的'gray' displayMode配置添加迁移逻辑
  - 为无效配置添加回退处理
  - 确保缺少isActive属性时的优雅降级
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [ ] 9. 更新工具函数和辅助函数
  - 更新任何引用旧DisplayMode类型的工具函数
  - 为灰色模式计算添加辅助函数
  - 更新颜色系统工具以使用正确的灰色
  - 确保所有辅助函数正确处理isActive属性
  - _需求: 1.1, 1.4, 5.1, 5.2_

- [ ] 10. 添加全面的单元测试
  - 测试GridCell在灰色模式和基础显示模式所有组合下的渲染
  - 测试灰色模式下isActive为true/false的场景
  - 测试DisplayModeControl组件行为
  - 测试配置验证和迁移
  - 测试存储操作和状态更新
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 4.1, 4.2, 5.4_

- [ ] 11. 添加模式切换的集成测试
  - 测试启用灰色模式时基础模式之间的切换
  - 测试在不同基础模式下启用/禁用灰色模式
  - 测试坐标和数值模式之间的互斥关系
  - 测试33x33网格渲染的性能
  - _需求: 6.1, 6.2, 6.3, 6.4, 7.1, 7.2, 7.3, 7.4_

- [ ] 12. 更新文档和示例
  - 更新组件文档以反映新的灰色模式行为
  - 添加显示灰色模式与不同基础显示模式的示例
  - 记录数值模式和颜色模式之间的关系
  - 更新新接口的API文档
  - _需求: 5.3, 6.1, 6.2, 6.3, 6.4_