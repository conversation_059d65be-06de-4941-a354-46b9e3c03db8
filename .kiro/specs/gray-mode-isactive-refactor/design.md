# 设计文档

## 概述

本文档概述了重构灰色模式实现的设计，以正确使用 `cellData.isActive` 属性。当前实现错误地将灰色模式视为单独的显示模式，并使用黑色作为默认颜色。新设计将灰色模式实现为可应用于三种基础显示模式（颜色、坐标、数值）的过滤器，并正确处理 `isActive` 状态。

## 架构设计

### 当前架构问题

1. **错误的模式结构**: 灰色模式当前被实现为四种显示模式之一：`'value' | 'coordinates' | 'color' | 'gray'`
2. **错误的默认颜色**: 使用黑色作为默认颜色而不是正确的灰色
3. **不完整的逻辑**: 灰色模式没有正确处理三种子显示模式
4. **不一致的行为**: 灰色模式下的坐标模式行为不符合需求

### 新架构设计

#### 显示模式关系

新架构定义了三种基础显示模式及其特定关系：

1. **颜色模式**: 仅显示单元格颜色，无文字覆盖
2. **数值模式**: 颜色模式的子集，同时显示颜色和数值映射（彩色单元格显示1-8，特殊坐标单元格显示A-M）
3. **坐标模式**: 显示单元格坐标，与数值模式互斥

**关键关系**：

- 数值模式扩展颜色模式（颜色 + 文字）
- 坐标和数值模式互斥
- 灰色模式可作为过滤器应用于任何基础模式

#### 类型系统重构

```typescript
// 新的基础显示模式（从DisplayMode中移除'gray'）
export type BaseDisplayMode = 'value' | 'coordinates' | 'color';

// 显示模式关系：
// - 'color': 仅显示颜色
// - 'value': 颜色模式的子集，显示颜色 + 数值映射（1-8, A-M）
// - 'coordinates': 显示坐标，与数值模式互斥

// 灰色模式配置
export interface GrayModeConfig {
  enabled: boolean;
  baseDisplayMode: BaseDisplayMode;
  defaultGrayColor: string; // 例如：'#f3f4f6'
}

// 更新的GridConfig接口
export interface GridConfig {
  // ... 现有属性
  displayMode: BaseDisplayMode; // 移除'gray'选项
  grayMode: GrayModeConfig;     // 新的灰色模式配置
}
```

#### 显示逻辑流程

```mermaid
graph TD
    A[单元格渲染请求] --> B{灰色模式启用?}
    B -->|否| C[正常显示逻辑]
    B -->|是| D{isActive?}
    D -->|false| E[显示默认灰色]
    D -->|true| F{基础显示模式}
    F -->|color| G[仅显示单元格颜色]
    F -->|coordinates| H[显示坐标]
    F -->|value| I[显示颜色 + 数值映射]
    C --> J[渲染单元格]
    E --> J
    G --> J
    H --> J
    I --> J
```

## 组件和接口

### 核心组件更新

#### 1. GridCell 组件

**当前问题：**

- 第256行：`if (cell.color === '#000000' || cell.color === 'black')` - 错误的黑色逻辑
- 第301行：灰色模式被视为独立的显示模式
- 灰色模式的文本内容逻辑不完整

**新实现：**
```typescript
// 更新的单元格背景颜色逻辑
const getCellBackgroundColor = useMemo(() => {
  // 灰色模式逻辑
  if (config.grayMode.enabled) {
    if (!isActive) {
      return config.grayMode.defaultGrayColor; // 使用正确的灰色，不是黑色
    }
    // isActive = true: 根据基础显示模式显示内容
    switch (config.displayMode) {
      case 'color':
        return cell.color || config.grayMode.defaultGrayColor;
      case 'coordinates':
      case 'value':
        return config.grayMode.defaultGrayColor; // 灰色背景配文字覆盖
    }
  }
  
  // 正常模式逻辑（现有）
  if (!isActive) {
    return config.theme === 'minimal' ? '#f3f4f6' : '#e5e7eb';
  }
  
  return cell.color || '#ffffff';
}, [config, isActive, cell.color]);

// 更新的单元格内容逻辑
const getCellContent = useMemo(() => {
  if (config.grayMode.enabled) {
    if (!isActive) {
      return null; // 灰色模式下非激活单元格无内容
    }
    
    // 激活单元格：根据基础显示模式显示内容
    switch (config.displayMode) {
      case 'value':
        // 数值模式是颜色模式的子集：显示颜色 + 数值映射
        return getCellValueContent(cell); // 返回颜色的1-8，特殊坐标的A-M
      case 'coordinates':
        return `${cell.x},${cell.y}`; // 与数值模式互斥
      case 'color':
        return null; // 颜色模式仅显示颜色，无文字覆盖
    }
  }
  
  // 正常模式逻辑（现有）
  // ... 现有实现
}, [config, isActive, cell]);
```

#### 2. DisplayModeControl 组件

**当前问题：**

- 灰色模式作为单选按钮选项包含在内
- 没有单独的灰色模式切换

**新实现：**
```typescript
interface DisplayModeControlProps {
  baseDisplayMode: BaseDisplayMode;
  grayModeEnabled: boolean;
  onBaseDisplayModeChange: (mode: BaseDisplayMode) => void;
  onGrayModeToggle: (enabled: boolean) => void;
}

// 组件结构：
// 1. 基础显示模式的单选按钮：
//    - 颜色：仅显示颜色
//    - 数值：显示颜色 + 数值映射（颜色模式的子集）
//    - 坐标：显示坐标（与数值模式互斥）
// 2. 灰色模式的单独切换/复选框
// 3. 灰色模式激活时的清晰视觉指示
// 4. UI应指示数值模式与颜色模式的关联关系
```

#### 3. StylePanel 组件

**需要的更新：**

- 从显示模式选项中移除灰色模式
- 添加灰色模式切换控件
- 更新状态管理以处理基础模式和灰色模式

### 数据模型

#### 更新的 CellData 接口

```typescript
export interface CellData {
  x: number;
  y: number;
  color?: string;
  value?: string | number;
  level: number;
  group: number | null;
  isActive: boolean; // 此属性驱动灰色模式行为
  // ... 其他现有属性
}
```

#### 默认配置

```typescript
export const DEFAULT_GRAY_MODE_CONFIG: GrayModeConfig = {
  enabled: false,
  baseDisplayMode: 'value',
  defaultGrayColor: '#f3f4f6', // 一致的灰色，不是黑色
};

export const DEFAULT_GRID_CONFIG: GridConfig = {
  // ... 现有默认值
  displayMode: 'value', // 移除'gray'作为默认选项
  grayMode: DEFAULT_GRAY_MODE_CONFIG,
};
```

## 错误处理

### 验证更新

1. **类型验证**: 更新所有验证函数以使用 `BaseDisplayMode` 而不是 `DisplayMode`
2. **迁移逻辑**: 处理具有 `displayMode: 'gray'` 的现有配置
3. **回退行为**: 确保灰色模式配置无效时的优雅降级

### 错误场景

1. **无效的基础显示模式**: 回退到 'value' 模式
2. **缺少 isActive 属性**: 为安全起见默认为 `false`
3. **无效的灰色颜色**: 使用系统默认灰色
4. **配置迁移**: 将旧的灰色模式设置转换为新结构

## 测试策略

### 单元测试

1. **GridCell 渲染测试**:
   - 测试灰色模式启用/禁用状态
   - 测试 isActive true/false 组合
   - 测试所有基础显示模式与灰色模式的组合
   - 测试颜色渲染（无黑色默认值）

2. **显示模式控制测试**:
   - 测试基础模式切换
   - 测试灰色模式切换
   - 测试状态同步

3. **配置测试**:
   - 测试默认配置生成
   - 测试配置验证
   - 测试从旧格式的迁移

### 集成测试

1. **模式切换流程**:
   - 测试启用灰色模式时基础模式之间的切换
   - 测试数值模式作为颜色模式子集的行为
   - 测试坐标和数值模式之间的互斥关系
   - 测试在不同基础模式下启用/禁用灰色模式
   - 测试 33x33 网格的性能

2. **状态管理测试**:
   - 测试状态持久化
   - 测试组件重新渲染优化
   - 测试属性钻取消除

### 视觉回归测试

1. **灰色模式外观**:
   - 比较不同基础模式下的灰色模式渲染
   - 验证无黑色颜色伪影
   - 测试非激活与激活单元格的视觉区别

2. **过渡平滑性**:
   - 测试模式切换动画
   - 验证过渡期间无闪烁

## 性能考虑

### 渲染优化

1. **记忆化策略**:
   - 记忆化灰色模式计算
   - 缓存颜色计算
   - 优化重新渲染触发器

2. **状态更新批处理**:
   - 批处理灰色模式和基础模式更新
   - 最小化不必要的重新渲染
   - 维持 33x33 网格性能要求

### 内存管理

1. **配置存储**: 灰色模式设置的高效存储
2. **颜色缓存**: 缓存计算的灰色以避免重新计算
3. **事件处理器优化**: 防止模式切换中的内存泄漏

## 迁移策略

### 向后兼容性

1. **配置迁移**:
   ```typescript
   function migrateDisplayModeConfig(oldConfig: any): GridConfig {
     if (oldConfig.displayMode === 'gray') {
       return {
         ...oldConfig,
         displayMode: 'value', // 默认基础模式
         grayMode: {
           enabled: true,
           baseDisplayMode: 'value',
           defaultGrayColor: '#f3f4f6'
         }
       };
     }
     return oldConfig;
   }
   ```

2. **组件属性**: 尽可能维护现有的属性接口
3. **状态存储**: 更新 Zustand 存储以处理新的配置结构

### 部署策略

1. **功能标志**: 在功能标志后实现以安全推出
2. **渐进迁移**: 暂时支持新旧配置
3. **用户沟通**: 清楚指示灰色模式行为变化