# Requirements Document

## Introduction

重构【灰色】模式的实现逻辑，移除现有的错误实现（使用黑色作为默认颜色），并基于`cellData.isActive`属性实现新的灰色显示逻辑。新的灰色模式应该：当`isActive`为false时显示默认灰色，当`isActive`为true时根据当前【显示模式】（颜色/坐标/数值）显示相应内容。

## Requirements

### Requirement 1

**User Story:** 作为用户，我希望灰色模式能够正确基于单元格的`isActive`状态显示内容，以便清晰区分激活和未激活的单元格。

#### Acceptance Criteria

1. WHEN 单元格的`isActive`为false THEN 系统 SHALL 显示默认灰色而不是黑色
2. WHEN 单元格的`isActive`为true THEN 系统 SHALL 根据当前显示模式显示相应内容
3. WHEN 用户切换到灰色模式 THEN 系统 SHALL 立即应用基于`isActive`的显示逻辑
4. WHEN `isActive`状态发生变化 THEN 灰色模式下的显示 SHALL 实时更新

### Requirement 2

**User Story:** 作为用户，我希望在灰色模式下，激活的单元格能够根据【显示模式】正确显示颜色信息，以便在灰色背景下识别不同颜色的单元格。

#### Acceptance Criteria

1. WHEN 显示模式为【颜色】且`isActive`为true THEN 系统 SHALL 显示单元格的实际颜色
2. WHEN 显示模式为【颜色】且`isActive`为false THEN 系统 SHALL 显示默认灰色
3. WHEN 切换显示模式 THEN 灰色模式下激活单元格的显示 SHALL 相应更新
4. WHEN 所有单元格的`isActive`都为true THEN 灰色模式 SHALL 等同于颜色模式

### Requirement 3

**User Story:** 作为用户，我希望在灰色模式下，当显示模式为【坐标】时能够显示坐标信息，以便在需要时识别单元格位置。

#### Acceptance Criteria

1. WHEN 显示模式为【坐标】THEN 系统 SHALL 显示所有单元格的坐标，不受`isActive`状态影响
2. WHEN 显示模式为【坐标】THEN 系统 SHALL 在灰色背景上清晰显示坐标文字
3. WHEN 坐标显示模式激活 THEN 系统 SHALL 确保坐标文字的可读性
4. WHEN 【坐标】和【数值】模式切换 THEN 系统 SHALL 确保同一时间只显示一种内容类型

### Requirement 4

**User Story:** 作为用户，我希望在灰色模式下，当显示模式为【数值】时能够显示数值信息，以便查看颜色映射值和特殊坐标字符。

#### Acceptance Criteria

1. WHEN 显示模式为【数值】且`isActive`为true THEN 系统 SHALL 显示颜色映射的数字（1-8）或特殊坐标字符（A-M）
2. WHEN 显示模式为【数值】且`isActive`为false THEN 系统 SHALL 显示默认灰色，不显示数值
3. WHEN 单元格为彩色且`isActive`为true THEN 系统 SHALL 显示对应的数字映射值（1-8）
4. WHEN 【数值】和【坐标】模式切换 THEN 系统 SHALL 确保同一时间只显示一种内容类型

### Requirement 5

**User Story:** 作为开发者，我希望移除现有的错误灰色模式实现，确保代码的一致性和可维护性。

#### Acceptance Criteria

1. WHEN 重构完成 THEN 系统 SHALL 完全移除使用黑色作为默认颜色的错误逻辑
2. WHEN 重构完成 THEN 系统 SHALL 使用统一的灰色值作为未激活单元格的显示颜色
3. WHEN 代码审查 THEN 所有相关组件 SHALL 使用一致的`isActive`检查逻辑
4. WHEN 性能测试 THEN 新的灰色模式实现 SHALL 保持33x33网格的渲染性能要求

### Requirement 6

**User Story:** 作为用户，我希望显示模式的切换逻辑清晰明确，确保【颜色】、【坐标】、【数值】三种模式的正确关系。

#### Acceptance Criteria

1. WHEN 用户选择【颜色】模式 THEN 系统 SHALL 显示颜色，不显示任何文字覆盖
2. WHEN 用户选择【数值】模式 THEN 系统 SHALL 显示颜色和数值映射，因为【数值】模式是【颜色】模式的子集
3. WHEN 用户选择【坐标】模式 THEN 系统 SHALL 显示坐标，与【数值】模式互斥
4. WHEN 【坐标】和【数值】模式切换 THEN 系统 SHALL 确保同一时间只显示一种内容类型
5. WHEN 【数值】模式激活 THEN 系统 SHALL 同时显示颜色背景和数值文字，体现其作为【颜色】模式子集的特性

### Requirement 7

**User Story:** 作为用户，我希望灰色模式的切换和使用体验流畅自然，不影响其他功能的正常使用。

#### Acceptance Criteria

1. WHEN 切换到灰色模式 THEN 系统 SHALL 平滑过渡，无闪烁或延迟
2. WHEN 在灰色模式下操作 THEN 系统 SHALL 保持所有交互功能的正常工作
3. WHEN 从灰色模式切换到其他模式 THEN 系统 SHALL 正确恢复原有的显示状态
4. WHEN 灰色模式激活 THEN 系统 SHALL 保持用户界面的响应性和稳定性