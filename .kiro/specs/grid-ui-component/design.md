# 设计文档

## 概述

本设计将现有的 grid-system 功能模块转换为可重用的 UI 组件，位于 `components/grid-system` 目录中。新的网格组件将是一个纯 UI 组件，专注于视觉呈现和客户端状态管理，不依赖服务端状态。组件将提供矩阵容器和格子单元的 UI 实现，支持灵活的配置和样式定制。

## 架构

### 组件层次结构

```
components/
├── grid-system/
│   ├── GridMatrix.tsx          # 主网格容器组件
│   ├── GridCell.tsx            # 单个格子组件
│   ├── hooks/
│   │   ├── useGridData.ts      # 网格数据管理 hook
│   │   ├── useGridConfig.ts    # 网格配置管理 hook
│   │   └── useGridAnimation.ts # 网格动画效果 hook
│   ├── types.ts                # 组件类型定义
│   └── index.ts                # 统一导出
```

### 数据流架构

```
basicDataStore (Zustand) 
    ↓
useGridData hook
    ↓
GridMatrix component
    ↓
GridCell components
```

### 控制面板集成

```
style-management (简化版)
    ↓
displayMode 控制
    ↓
GridMatrix props
    ↓
格子内容显示更新
```

## 组件和接口

### GridMatrix 组件

主要的网格容器组件，负责渲染整个网格矩阵。

```typescript
interface GridMatrixProps {
  config: GridConfig;
  className?: string;
  onCellClick?: (cell: CellData, event: MouseEvent) => void;
  onCellHover?: (cell: CellData | null) => void;
}

interface GridConfig {
  size: number;                    // 网格尺寸 (16x16)
  cellShape: 'square' | 'rounded'; // 格子形状
  theme: 'colorful' | 'minimal';   // 主题样式
  displayMode: 'value' | 'coordinates' | 'color'; // 显示模式
  gap: number;                     // 格子间距
  padding: number;                 // 容器内边距
  fontSize: number;                // 字体大小
  scale: {
    enabled: boolean;              // 是否启用缩放
    factor: number;                // 缩放倍数
  };
  animation: {
    enabled: boolean;              // 是否启用动画
    duration: number;              // 动画持续时间 (ms)
  };
}
```

### GridCell 组件

单个格子组件，负责渲染单个网格单元。

```typescript
interface GridCellProps {
  cell: CellData;
  config: GridConfig;
  isActive: boolean;
  onClick?: (cell: CellData, event: MouseEvent) => void;
  onHover?: (cell: CellData | null) => void;
}

interface CellData {
  x: number;                       // X 坐标
  y: number;                       // Y 坐标
  color?: string;                  // 关联颜色
  value?: number;                  // 颜色映射数值
  level?: number;                  // 颜色级别
  group?: number | null;           // 分组信息
}
```

### 样式管理控制面板 (简化版)

```typescript
interface GridControlPanelProps {
  displayMode: 'value' | 'coordinates' | 'color';
  onDisplayModeChange: (mode: 'value' | 'coordinates' | 'color') => void;
}
```

## 数据模型

### 网格状态管理

使用现有的 `basicDataStore` 作为数据源：

```typescript
interface GridState {
  // 从 basicDataStore 获取
  colorCoordinates: Record<BasicColorType, ColorCoordinates>;
  colorVisibility: Record<BasicColorType, ColorVisibility>;
  blackCellData: BlackCellData;
  
  // 组件内部状态
  displayMode: 'value' | 'coordinates' | 'color';
  hoveredCell: CellData | null;
  selectedCells: Set<string>;
}
```

### 格子激活逻辑

```typescript
interface CellActivationLogic {
  // 检查格子是否应该激活
  isCellActive(x: number, y: number): boolean;
  
  // 获取格子关联的颜色
  getCellColor(x: number, y: number): string | null;
  
  // 获取格子显示内容
  getCellContent(cell: CellData, displayMode: string): string | null;
}
```

## 错误处理

### 组件级错误边界

```typescript
interface GridErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

// 错误恢复策略
const errorRecoveryStrategies = {
  dataLoadError: () => '显示默认网格',
  renderError: () => '显示错误占位符',
  configError: () => '使用默认配置'
};
```

### 数据验证

```typescript
interface DataValidation {
  validateGridConfig(config: GridConfig): ValidationResult;
  validateCellData(cell: CellData): ValidationResult;
  sanitizeCoordinates(x: number, y: number): [number, number];
}
```

## 测试策略

### 单元测试

1. **组件渲染测试**
   - GridMatrix 正确渲染网格结构
   - GridCell 正确显示格子状态
   - 配置变更时组件正确更新

2. **交互测试**
   - 格子点击事件处理
   - 悬停状态变化
   - 键盘导航支持

3. **状态管理测试**
   - useGridData hook 正确获取数据
   - useGridConfig hook 正确管理配置
   - 状态变更时组件正确响应

### 集成测试

1. **与 basicDataStore 集成**
   - 数据正确从 store 获取
   - 状态变更正确同步
   - 错误状态正确处理

2. **与控制面板集成**
   - displayMode 变更正确传递
   - 控制面板状态与网格同步
   - 用户交互流程完整

### 性能测试

1. **渲染性能**
   - 大网格 (16x16) 渲染性能
   - 频繁状态更新性能
   - 动画效果性能

2. **内存使用**
   - 组件内存占用
   - 状态管理内存效率
   - 事件监听器清理

## 实现注意事项

### 性能优化

1. **React.memo 优化**
   - GridCell 组件使用 memo 避免不必要重渲染
   - 合理的 props 比较函数

2. **状态更新优化**
   - 使用 useCallback 缓存事件处理函数
   - 使用 useMemo 缓存计算结果

3. **动画性能**
   - 使用 CSS transforms 而非改变布局属性
   - 合理的动画帧率控制

### 可访问性

1. **键盘导航**
   - 支持 Tab 键在格子间导航
   - 支持方向键移动焦点
   - 支持 Enter/Space 激活格子

2. **屏幕阅读器支持**
   - 合适的 ARIA 标签
   - 格子状态的语义化描述
   - 网格结构的清晰表达

3. **视觉辅助**
   - 高对比度模式支持
   - 焦点指示器清晰可见
   - 颜色信息的替代表达

### 兼容性考虑

1. **浏览器兼容性**
   - 现代浏览器 CSS Grid 支持
   - CSS 自定义属性 fallback
   - 动画效果的渐进增强

2. **响应式设计**
   - 不同屏幕尺寸的适配
   - 触摸设备的交互优化
   - 移动端性能考虑