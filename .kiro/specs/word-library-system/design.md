# 设计文档

## 概述

词库系统是一个综合性功能，为33x33网格系统添加中文词语绑定能力。系统采用分层架构设计，包括词库管理、单元格词语绑定、键盘导航选择、重复检测和版本管理等核心功能。设计遵循现有的Zustand状态管理模式和组件架构原则。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "UI层"
        A[GridCell组件] --> B[词语显示覆盖层]
        C[控制面板] --> D[词库管理面板]
        E[词语选择界面] --> F[键盘导航]
    end
    
    subgraph "状态管理层"
        G[wordLibraryStore] --> H[词库数据]
        G --> I[单元格绑定]
        G --> J[版本管理]
        K[gridConfigStore] --> L[显示模式]
    end
    
    subgraph "业务逻辑层"
        M[词语绑定服务] --> N[重复检测]
        O[版本管理服务] --> P[数据持久化]
        Q[键盘导航服务] --> R[选择状态管理]
    end
    
    subgraph "数据层"
        S[LocalStorage] --> T[词库持久化]
        S --> U[绑定关系持久化]
        S --> V[版本数据持久化]
    end
    
    A --> G
    C --> G
    E --> M
    G --> S
```

### 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant Cell as GridCell
    participant Nav as 键盘导航
    participant Store as wordLibraryStore
    participant Service as 词语服务
    
    User->>Cell: 双击单元格
    Cell->>Store: 激活填词模式
    Store->>Nav: 初始化导航状态
    Nav->>User: 显示词语选择
    
    User->>Nav: 按左右键导航
    Nav->>Store: 更新选择位置
    Store->>Cell: 预览词语显示
    
    User->>Nav: 按回车确认
    Nav->>Service: 绑定词语到单元格
    Service->>Store: 更新绑定关系
    Store->>Cell: 持久化显示词语
```

## 组件和接口

### 核心组件结构

#### 1. WordLibraryStore (状态管理)

```typescript
interface WordLibraryStore {
  // 词库数据
  wordLibrary: WordLibraryData;
  
  // 单元格绑定
  cellWordBindings: Map<string, string>; // key: "x,y", value: wordId
  
  // 填词版本
  wordVersions: WordVersion[];
  
  // 导航状态
  navigationState: NavigationState;
  
  // 操作方法
  addWord: (word: WordEntry) => void;
  removeWord: (wordId: string) => void;
  bindWordToCell: (x: number, y: number, wordId: string) => void;
  unbindWordFromCell: (x: number, y: number) => void;
  saveWordVersion: (name: string) => void;
  loadWordVersion: (versionId: string) => void;
  
  // 导航方法
  activateWordSelection: (x: number, y: number) => void;
  navigateWords: (direction: 'left' | 'right') => void;
  confirmSelection: () => void;
  cancelSelection: () => void;
}
```

#### 2. WordLibraryPanel (控制面板组件)

```typescript
interface WordLibraryPanelProps {
  isActive: boolean;
  onWordAdd: (word: WordEntry) => void;
  onWordEdit: (wordId: string, updates: Partial<WordEntry>) => void;
  onWordDelete: (wordId: string) => void;
}

// 支持按颜色和等级分类的词库显示
interface WordLibraryData {
  categories: Record<BasicColorType, Record<ColorLevel, WordEntry[]>>;
  metadata: {
    totalWords: number;
    lastUpdated: Date;
  };
}
```

#### 3. WordSelectionOverlay (词语选择界面)

```typescript
interface WordSelectionOverlayProps {
  isVisible: boolean;
  targetCell: { x: number; y: number } | null;
  availableWords: WordEntry[];
  selectedIndex: number;
  onNavigate: (direction: 'left' | 'right') => void;
  onConfirm: () => void;
  onCancel: () => void;
}
```

#### 4. WordVersionPanel (版本管理面板)

```typescript
interface WordVersionPanelProps {
  versions: WordVersion[];
  currentVersion: string | null;
  onSave: (name: string) => void;
  onLoad: (versionId: string) => void;
  onDelete: (versionId: string) => void;
  onRename: (versionId: string, newName: string) => void;
}
```

### 数据模型

#### WordEntry (词语条目)

```typescript
interface WordEntry {
  id: string;                    // 唯一标识符
  text: string;                  // 中文词语文本
  category: {
    color: BasicColorType;       // 所属颜色类别
    level: ColorLevel;           // 所属等级
  };
  metadata: {
    createdAt: Date;            // 创建时间
    usageCount: number;         // 使用次数
    lastUsed?: Date;            // 最后使用时间
  };
  isDuplicate?: boolean;        // 是否为重复词语
  usagePositions?: Array<{x: number, y: number}>; // 使用位置
}
```

#### WordVersion (填词版本)

```typescript
interface WordVersion {
  id: string;                   // 版本唯一标识符
  name: string;                 // 版本名称
  description?: string;         // 版本描述
  createdAt: Date;             // 创建时间
  bindings: Map<string, string>; // 单元格绑定关系快照
  metadata: {
    totalBindings: number;      // 绑定总数
    coverageRate: number;       // 覆盖率 (绑定单元格数/总单元格数)
  };
}
```

#### NavigationState (导航状态)

```typescript
interface NavigationState {
  isActive: boolean;            // 是否处于选择模式
  targetCell: { x: number; y: number } | null; // 目标单元格
  availableWords: WordEntry[];  // 可选词语列表
  selectedIndex: number;        // 当前选择索引
  lastSelectedIndex: Map<string, number>; // 记录每个分类的上次选择位置
  previewMode: boolean;         // 是否显示预览
}
```

## 错误处理

### 错误类型定义

```typescript
enum WordLibraryErrorType {
  INVALID_CHINESE_TEXT = 'INVALID_CHINESE_TEXT',
  DUPLICATE_WORD = 'DUPLICATE_WORD',
  WORD_NOT_FOUND = 'WORD_NOT_FOUND',
  BINDING_CONFLICT = 'BINDING_CONFLICT',
  VERSION_SAVE_FAILED = 'VERSION_SAVE_FAILED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED'
}

interface WordLibraryError {
  type: WordLibraryErrorType;
  message: string;
  context?: any;
}
```

### 错误处理策略

1. **词语验证错误**: 显示内联错误提示，阻止无效操作
2. **存储错误**: 提供降级方案，使用内存存储
3. **版本冲突**: 提供合并选项或覆盖确认
4. **网络错误**: 实现离线模式，本地缓存优先

## 测试策略

### 单元测试覆盖

1. **词库管理功能**
   - 词语添加、编辑、删除
   - 中文字符验证
   - 重复检测逻辑

2. **单元格绑定功能**
   - 绑定关系建立和解除
   - 多单元格绑定同一词语
   - 绑定状态持久化

3. **键盘导航功能**
   - 方向键导航逻辑
   - 循环选择行为
   - 跨分类导航

4. **版本管理功能**
   - 版本保存和加载
   - 版本数据完整性
   - 版本间切换

### 集成测试场景

1. **完整填词流程**
   - 双击激活 → 键盘导航 → 确认绑定
   - 词语显示与网格渲染集成
   - 与现有颜色/等级系统协同

2. **性能测试**
   - 1089个单元格全部绑定词语的渲染性能
   - 大量词库数据的搜索和过滤性能
   - 版本切换的响应时间

3. **用户体验测试**
   - 键盘导航的流畅性
   - 词语显示的可读性
   - 重复检测的视觉反馈

### 端到端测试

使用Playwright实现关键用户场景的自动化测试：

1. 创建词库并添加词语
2. 双击单元格进行填词
3. 使用键盘导航选择词语
4. 保存和加载填词版本
5. 重复词语的检测和高亮

## 性能考虑

### 渲染优化

1. **词语显示优化**
   - 使用CSS transform进行位置调整
   - 实现词语显示的虚拟化（仅渲染可见区域）
   - 词语文本的字体和大小自适应

2. **状态更新优化**
   - 批量更新单元格绑定状态
   - 使用useMemo缓存计算结果
   - 防抖键盘导航事件

3. **内存管理**
   - 词库数据的懒加载
   - 版本数据的压缩存储
   - 及时清理未使用的绑定关系

### 存储优化

1. **数据结构优化**
   - 使用Map结构提高查找效率
   - 词语索引的建立和维护
   - 版本数据的增量存储

2. **持久化策略**
   - 关键数据的实时保存
   - 非关键数据的延迟保存
   - 存储配额的监控和管理

## 可访问性

### 键盘导航支持

1. **标准键盘操作**
   - Tab键在词库面板中导航
   - 方向键在词语选择中导航
   - Enter/Space确认操作
   - Escape取消操作

2. **屏幕阅读器支持**
   - 为词语添加aria-label
   - 词库面板的语义化标记
   - 选择状态的语音反馈

3. **视觉辅助**
   - 高对比度模式支持
   - 词语显示的字体大小调节
   - 重复词语的多种视觉指示

## 国际化考虑

虽然当前版本专注于中文词语，但设计考虑了未来的扩展性：

1. **文本验证的可扩展性**
   - 抽象的字符验证接口
   - 支持不同语言的词语规则

2. **UI文本的国际化**
   - 所有界面文本使用i18n键
   - 错误消息的多语言支持

3. **字体和排版**
   - 支持不同语言的字体回退
   - 文本方向的适配（LTR/RTL）