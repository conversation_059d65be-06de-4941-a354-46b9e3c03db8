# Design Document

## Overview

本设计文档旨在修正【显示模式】中【数值】功能的显示逻辑，确保正确显示颜色数字映射`mappingValue`和黑色格子坐标字符映射`SPECIAL_COORDINATES`。

当前系统存在的问题：
1. 在数值显示模式下，颜色单元格显示的是`level`而不是`mappingValue`
2. 黑色格子显示逻辑不一致，【数值】模式下显示字母，
3. 数值显示逻辑分散在多个组件中，缺乏统一的处理机制

## Architecture

### 核心组件架构
```
GridMatrix (主网格组件)
├── GridCell (单元格组件) - 负责渲染单个单元格
├── useGridData (数据Hook) - 负责数据处理和内容生成
└── useGridConfig (配置Hook) - 负责配置验证和管理
```

### 数据流架构
```
basicDataStore (数据源)
├── DEFAULT_COLOR_VALUES (包含mappingValue)
├── SPECIAL_COORDINATES (黑色格子字符映射)
└── CellData (单元格数据结构)
    ↓
useGridData (数据处理)
├── getCellContent() - 根据显示模式生成内容
└── coordinateMap - 坐标到颜色信息的映射
    ↓
GridCell (渲染组件)
└── cellContent - 最终显示的内容
```

## Components and Interfaces

### 1. 数据接口修正

#### CellData接口增强
```typescript
interface CellData {
  x: number;
  y: number;
  index: number;
  color: string;
  colorMappingValue: number; // 已存在，需要正确使用
  level: number;
  group: number | null;
  // 新增：用于统一数值显示的计算属性
  displayValue?: string | number;
}
```

#### 显示内容生成接口
```typescript
interface DisplayContentGenerator {
  getValueModeContent(cell: CellData): string | null;
  getColorMappingValue(colorType: BasicColorType): number;
  getSpecialCoordinateChar(x: number, y: number): string | null;
}
```

### 2. 核心组件修正

#### GridCell组件修正
- 修正`cellContent`的计算逻辑
- 确保数值模式下正确显示`mappingValue`和`SPECIAL_COORDINATES`
- 统一错误处理机制

#### useGridData Hook修正
- 修正`getCellContent`函数的数值模式逻辑
- 集成`mappingValue`和`SPECIAL_COORDINATES`的查询
- 优化性能，避免重复计算

### 3. 数据映射服务

#### ColorMappingService
```typescript
class ColorMappingService {
  static getMappingValue(colorType: BasicColorType): number;
  static getColorTypeFromHex(hex: string): BasicColorType | null;
  static isValidMappingValue(value: number): boolean;
}
```

#### SpecialCoordinateService
```typescript
class SpecialCoordinateService {
  static getCharacter(x: number, y: number): string | null;
  static isSpecialCoordinate(x: number, y: number): boolean;
  static getAllSpecialCoordinates(): Array<{coords: [number, number], letter: string}>;
}
```

## Data Models

### 1. 颜色映射数据模型

```typescript
// 已存在的DEFAULT_COLOR_VALUES结构
const DEFAULT_COLOR_VALUES: Record<BasicColorType, ColorValue> = {
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 },
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0] } // 无mappingValue
};
```

### 2. 特殊坐标映射数据模型

```typescript
// 已存在的SPECIAL_COORDINATES结构
const SPECIAL_COORDINATES = new Map([
  ['0,0', 'A'],
  ['16,0', 'B'],
  // ... 其他坐标映射
]);
```

### 3. 数值显示优先级模型

```typescript
interface ValueDisplayPriority {
  // 优先级：黑色格子字符 > 颜色映射值 > 默认值
  priority: 'special_coordinate' | 'color_mapping' | 'default';
  value: string | number;
  source: 'SPECIAL_COORDINATES' | 'mappingValue' | 'level';
}
```

## Error Handling

### 1. 数据验证错误处理

```typescript
interface ValueDisplayError {
  type: 'missing_mapping' | 'invalid_coordinate' | 'data_inconsistency';
  message: string;
  fallbackValue: string | number;
  recoverable: boolean;
}
```

### 2. 错误恢复策略

1. **缺失映射值**: 使用level作为fallback
2. **无效坐标**: 显示'?'或空白
3. **数据不一致**: 记录错误并使用默认值
4. **渲染错误**: 显示错误指示符'!'

### 3. 错误监控和日志

```typescript
interface ErrorReporting {
  logDisplayError(error: ValueDisplayError, context: CellData): void;
  reportInconsistency(expected: any, actual: any, cell: CellData): void;
  trackPerformanceIssues(renderTime: number, cellCount: number): void;
}
```

## Testing Strategy

### 1. 单元测试

#### 数值显示逻辑测试
- 测试颜色单元格显示正确的`mappingValue`
- 测试黑色格子显示正确的`SPECIAL_COORDINATES`字符
- 测试边界情况和错误处理

#### 数据映射服务测试
- 测试`ColorMappingService`的映射值获取
- 测试`SpecialCoordinateService`的字符查询
- 测试数据一致性验证

### 2. 集成测试

#### 组件集成测试
- 测试`GridCell`和`useGridData`的协作
- 测试显示模式切换的正确性
- 测试数据更新时的实时同步

#### 端到端测试
- 测试完整的用户交互流程
- 测试33x33网格的完整渲染
- 测试性能要求（<100ms渲染时间）

### 3. 性能测试

#### 渲染性能测试
- 测试1089个单元格的渲染时间
- 测试数值模式切换的响应时间
- 测试内存使用和垃圾回收

#### 数据处理性能测试
- 测试映射值查询的性能
- 测试特殊坐标查询的性能
- 测试大量数据更新的处理能力

### 4. 用户体验测试

#### 视觉一致性测试
- 测试数值显示的清晰度
- 测试不同显示模式的视觉区分
- 测试错误状态的用户友好性

#### 交互响应测试
- 测试显示模式切换的流畅性
- 测试数据更新的实时反馈
- 测试错误恢复的用户体验

## Implementation Notes

### 1. 性能优化考虑

- 使用`useMemo`缓存映射值查询结果
- 使用`useCallback`优化事件处理函数
- 避免在渲染过程中进行复杂计算

### 2. 向后兼容性

- 保持现有的`DisplayMode`类型定义
- 保持现有的组件接口不变
- 确保现有功能不受影响

### 3. 扩展性设计

- 支持未来添加新的显示模式
- 支持自定义映射值配置
- 支持动态特殊坐标配置

### 4. 代码质量要求

- 遵循TypeScript严格模式
- 保持代码的可读性和可维护性
- 添加充分的注释和文档