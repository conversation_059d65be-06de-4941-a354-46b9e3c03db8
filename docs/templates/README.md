# 文档模板目录

本目录提供Cube1 Group项目中各类文档的标准化模板，确保文档格式统一、内容完整。

---

## 📋 可用模板列表

### 📝 基础模板
- **[开发日志模板](./log-template.md)** - 日常开发记录和问题解决过程模板
- **[技术报告模板](./report-template.md)** - 技术分析、性能优化、问题修复报告模板
- **[架构文档模板](./architecture-template.md)** - 系统架构设计和分析文档模板

---

## 🎯 模板使用指南

### 选择合适的模板

#### 📊 技术报告模板 (`report-template.md`)
**适用场景**:
- 性能优化分析报告
- 问题修复详细记录
- 技术方案对比分析
- 代码重构总结报告

**包含内容**:
- 问题背景和目标
- 详细分析过程
- 解决方案实施
- 结果验证和总结

#### 🏗️ 架构文档模板 (`architecture-template.md`)
**适用场景**:
- 系统架构设计文档
- 组件架构说明
- 数据流架构分析
- 技术栈架构选型

**包含内容**:
- 架构概述和目标
- 详细设计说明
- 技术选型理由
- 实施计划和风险

#### 📅 开发日志模板 (`log-template.md`)
**适用场景**:
- 日常开发进度记录
- 问题排查过程记录
- 技术学习笔记
- 实验性功能开发记录

**包含内容**:
- 工作内容概述
- 具体实施步骤
- 遇到的问题和解决方案
- 下一步计划

---

## 🛠️ 模板使用流程

### 1. 选择模板
根据文档类型选择合适的模板文件

### 2. 复制模板
```bash
# 复制到目标目录
cp docs/templates/report-template.md docs/report/your-report-name.md
```

### 3. 重命名文件
按照[文件管理规范](../FILE_MANAGEMENT_GUIDELINES.md)重命名文件

### 4. 填写内容
- 替换模板中的占位符
- 填写具体的内容信息
- 更新文档头部元数据

### 5. 更新索引
在相应目录的README.md中添加新文档链接

---

## 📝 模板定制指南

### 头部信息标准
所有模板都包含标准化的头部信息：

```markdown
# 文档标题

**文档类型**: [报告/架构/日志]  
**创建日期**: YYYY-MM-DD  
**最后更新**: YYYY-MM-DD  
**作者**: [作者名称]  
**版本**: v1.0.0  
**状态**: [草稿/审核中/已完成]

## 📋 文档概述
[简要描述文档内容和目的]
```

### 内容结构规范
- **使用标准标题层级**: # ## ### #### 
- **包含必要的emoji**: 提高可读性
- **代码块语言标识**: 正确设置代码高亮
- **表格格式统一**: 使用标准Markdown表格
- **链接格式规范**: 使用相对路径链接

### 样式约定
- **强调文本**: 使用 `**粗体**` 和 `*斜体*`
- **代码引用**: 使用 `` `代码` `` 格式
- **列表格式**: 统一使用 `-` 或 `1.` 格式
- **分隔线**: 使用 `---` 作为章节分隔

---

## 🔧 模板维护

### 模板更新原则
1. **保持向后兼容**: 更新不应破坏现有文档
2. **征求反馈**: 重大更新前征求团队意见
3. **版本控制**: 重要更新需要版本号递增
4. **文档同步**: 更新模板后同步更新使用说明

### 模板质量检查
- **格式正确性**: 确保Markdown语法正确
- **链接有效性**: 检查模板中的链接
- **内容完整性**: 确保包含所有必要部分
- **示例清晰**: 提供清晰的填写示例

### 新模板创建
如需创建新模板：
1. **分析需求**: 确定新模板的必要性
2. **设计结构**: 基于现有模板设计结构
3. **编写内容**: 包含详细的填写说明
4. **测试验证**: 实际使用验证模板效果
5. **更新文档**: 在本README中添加说明

---

## 📊 模板使用统计

| 模板类型 | 使用频率 | 最后更新 | 状态 |
|----------|----------|----------|------|
| 技术报告模板 | 高 | 2025-07-27 | ✅ 活跃 |
| 架构文档模板 | 中 | 2025-07-27 | ✅ 活跃 |
| 开发日志模板 | 中 | 2025-07-27 | ✅ 活跃 |

---

## 🔍 快速参考

### 常用模板快速链接
- **写技术报告**: [report-template.md](./report-template.md)
- **设计架构文档**: [architecture-template.md](./architecture-template.md)
- **记录开发日志**: [log-template.md](./log-template.md)

### 相关文档
- **[文档管理规范](../FILE_MANAGEMENT_GUIDELINES.md)** - 了解完整的文档管理规范
- **[用户指南](../USER_GUIDELINES.md)** - AI助手编码行为规范
- **[项目文档总览](../README.md)** - 查看所有文档索引

---

## 💡 最佳实践建议

### 模板使用技巧
1. **先阅读模板**: 使用前完整阅读模板内容
2. **保留结构**: 保持模板的基本结构不变
3. **详细填写**: 尽可能详细地填写每个部分
4. **及时更新**: 文档内容变化时及时更新

### 常见问题避免
- **不要删除头部信息**: 保留完整的文档元数据
- **不要忽略索引更新**: 新文档要及时更新索引
- **不要混用格式**: 在同一文档中保持格式一致
- **不要忘记代码高亮**: 为代码块设置正确的语言标识

---

**模板维护**: 定期更新和优化模板内容  
**最后更新**: 2025-07-27  
**模板数量**: 3个标准模板  
**使用状态**: 全部活跃使用
