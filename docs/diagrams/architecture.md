# Cube1 Group 系统架构图

## 概述

本文档展示了 Cube1 Group 网格数据可视化系统的整体架构设计。系统采用现代化的全栈架构，专注于高性能 33x33 网格（1089 个单元格）的实时渲染和交互。

## 系统架构

```mermaid
graph TB
    %% 用户界面层
    subgraph "用户界面层 (UI Layer)"
        UI[Matrix组件<br/>33x33网格渲染]
        Controls[Controls组件<br/>模式切换控制]
        Button[Button组件<br/>统一按钮系统]
        Icons[Icons组件<br/>图标系统]
    end

    %% 状态管理层
    subgraph "状态管理层 (State Management)"
        Store[MatrixStore<br/>Zustand状态管理]
        Persist[持久化中间件<br/>数据持久化]
        Cache[计算属性缓存<br/>性能优化]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层 (Business Logic)"
        Core[MatrixCore<br/>核心处理引擎]
        Handlers[ModeHandlers<br/>业务模式处理器]
        Types[MatrixTypes<br/>统一类型系统]
        
        subgraph "业务模式 (Business Modes)"
            Coord[坐标模式<br/>coordinate]
            Color[颜色模式<br/>color]
            Level[等级模式<br/>level]
            Word[词语模式<br/>word]
        end
    end

    %% 数据层
    subgraph "数据层 (Data Layer)"
        GroupData[GroupAData<br/>A-M组数据源]
        DataCache[数据缓存<br/>CacheManager]
        DataGen[数据生成器<br/>generateGroupData]
    end

    %% 后端服务层
    subgraph "后端服务层 (Backend Services)"
        FastAPI[FastAPI应用<br/>main.py]
        Router[API路由<br/>v1/router.py]
        Health[健康检查<br/>health.py]
        Endpoints[API端点<br/>endpoints/]
    end

    %% 基础设施层
    subgraph "基础设施层 (Infrastructure)"
        NextJS[Next.js 15.1.0<br/>App Router]
        Turbo[Turbo Monorepo<br/>构建系统]
        PNPM[pnpm 9.15.0<br/>包管理]
        TypeScript[TypeScript 5.8.3<br/>类型系统]
    end

    %% 连接关系
    UI --> Store
    Controls --> Store
    Button --> UI
    Icons --> Controls
    
    Store --> Core
    Store --> Persist
    Store --> Cache
    
    Core --> Handlers
    Core --> Types
    Handlers --> Coord
    Handlers --> Color
    Handlers --> Level
    Handlers --> Word
    
    Core --> GroupData
    GroupData --> DataCache
    GroupData --> DataGen
    
    Store -.->|API调用| Router
    Router --> Health
    Router --> Endpoints
    FastAPI --> Router
    
    UI -.->|运行在| NextJS
    Store -.->|运行在| NextJS
    Core -.->|运行在| NextJS
    NextJS -.->|构建| Turbo
    Turbo -.->|包管理| PNPM
    NextJS -.->|类型检查| TypeScript

    %% 样式定义
    classDef uiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef stateLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef backendLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef infraLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class UI,Controls,Button,Icons uiLayer
    class Store,Persist,Cache stateLayer
    class Core,Handlers,Types,Coord,Color,Level,Word businessLayer
    class GroupData,DataCache,DataGen dataLayer
    class FastAPI,Router,Health,Endpoints backendLayer
    class NextJS,Turbo,PNPM,TypeScript infraLayer
```

## 架构层次说明

### 1. 用户界面层 (UI Layer)
- **Matrix组件**: 负责33x33网格的渲染和交互
- **Controls组件**: 提供模式切换和操作控制
- **Button组件**: 统一的按钮设计系统
- **Icons组件**: 图标系统

### 2. 状态管理层 (State Management)
- **MatrixStore**: 基于Zustand的响应式状态管理
- **持久化中间件**: 支持状态持久化和恢复
- **计算属性缓存**: 性能优化的缓存机制

### 3. 业务逻辑层 (Business Logic)
- **MatrixCore**: 核心处理引擎，统一的矩阵处理逻辑
- **ModeHandlers**: 业务模式处理器，支持插件式扩展
- **MatrixTypes**: 统一的类型系统
- **业务模式**: 支持坐标、颜色、等级、词语四种模式

### 4. 数据层 (Data Layer)
- **GroupAData**: A-M组数据源管理
- **数据缓存**: 高性能缓存管理器
- **数据生成器**: 动态数据生成功能

### 5. 后端服务层 (Backend Services)
- **FastAPI应用**: 基于FastAPI的后端服务
- **API路由**: 模块化的API路由管理
- **健康检查**: 系统健康状态监控
- **API端点**: 具体的业务端点

### 6. 基础设施层 (Infrastructure)
- **Next.js**: 前端框架和App Router
- **Turbo**: Monorepo构建系统
- **pnpm**: 包管理工具
- **TypeScript**: 类型系统支持

## 技术特性

- **高性能渲染**: 1089个单元格同时渲染，无需虚拟化
- **响应式状态管理**: 基于Zustand的高效状态管理
- **模块化架构**: 清晰的分层架构，易于维护和扩展
- **类型安全**: 完整的TypeScript类型系统
- **缓存优化**: 多层缓存机制，提升性能
- **插件式业务模式**: 支持灵活的业务模式扩展

## 数据流向

1. **用户交互** → UI组件 → 状态管理 → 业务逻辑 → 数据层
2. **数据更新** → 缓存检查 → 状态更新 → 视图重新渲染
3. **模式切换** → 业务逻辑处理 → 渲染数据生成 → UI更新

---

*生成时间: 2025-01-25*
*版本: v1.0*
