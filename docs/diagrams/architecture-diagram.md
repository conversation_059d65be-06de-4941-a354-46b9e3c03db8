# Cube1 Group 整体系统架构图

> 生成时间：2025年8月1日 22:10:56 CST
> 
> 本文档展示了 Cube1 Group 项目的整体系统架构，包括前端、后端、数据层和业务逻辑的完整架构关系。

## 架构概述

Cube1 Group 是一个现代化的全栈网格数据可视化系统，专注于高性能 33x33 网格（1089 个单元格）的实时渲染和交互。系统采用 Monorepo 架构，基于最新的前后端技术栈构建。

### 技术栈

- **前端**: Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3
- **后端**: FastAPI 0.116.1 + Python 3.11+
- **状态管理**: Zustand 5.0.6（替代了 80+ 个 useState 钩子）
- **样式**: Tailwind CSS 3.4.17
- **构建工具**: Turbo 2.3.0 + pnpm 9.15.0

### 核心特性

- **33x33网格矩阵**: 1089个可交互单元格的实时渲染
- **4种业务模式**: 坐标、颜色、等级、词语模式
- **响应式状态管理**: 基于Zustand的高性能状态管理
- **数据驱动架构**: 纯组件设计，完全数据驱动
- **性能优化**: 计算属性缓存和持久化机制

## 系统架构图

```mermaid
graph TB
    %% 定义样式
    classDef frontend fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef backend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef core fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef ui fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef api fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    %% Monorepo 层
    subgraph MONOREPO["🏗️ Monorepo 架构"]
        TURBO["Turbo 2.3.0<br/>+ pnpm 9.15.0"]
        WORKSPACE["工作区管理"]
        TURBO --> WORKSPACE
    end

    %% 前端架构
    subgraph FRONTEND["🎨 前端架构 (Next.js 15.1.0)"]
        %% UI 层
        subgraph UI_LAYER["📱 用户界面层"]
            MATRIX_COMP["Matrix.tsx<br/>33x33网格组件"]
            CONTROLS_COMP["Controls.tsx<br/>控制面板组件"]
            BUTTON_COMP["Button.tsx<br/>统一按钮组件"]
            ICONS_COMP["Icons.tsx<br/>图标系统"]
        end

        %% 状态管理层
        subgraph STATE_LAYER["🔄 状态管理层"]
            MATRIX_STORE["MatrixStore.ts<br/>Zustand状态管理"]
            PERSIST["持久化中间件<br/>LocalStorage"]
            CACHE["计算属性缓存<br/>性能优化"]
        end

        %% 业务逻辑层
        subgraph LOGIC_LAYER["⚙️ 业务逻辑层"]
            MATRIX_CORE["MatrixCore.ts<br/>核心处理引擎"]
            MODE_HANDLERS["ModeHandlers<br/>业务模式处理器"]
            MATRIX_TYPES["MatrixTypes.ts<br/>统一类型系统"]
        end

        %% 数据层
        subgraph DATA_LAYER["💾 数据层"]
            GROUP_DATA["GroupAData.ts<br/>A-M组数据源"]
            DATA_CACHE["数据缓存管理器<br/>高性能缓存"]
            DATA_GEN["数据生成器<br/>动态数据生成"]
        end
    end

    %% 后端架构
    subgraph BACKEND["🚀 后端架构 (FastAPI 0.116.1)"]
        %% API 层
        subgraph API_LAYER["🌐 API 服务层"]
            FASTAPI_APP["FastAPI应用<br/>main.py"]
            API_ROUTER["API路由器<br/>v1/router.py"]
            HEALTH_EP["健康检查端点<br/>health.py"]
        end

        %% 服务层
        subgraph SERVICE_LAYER["🔧 服务层"]
            BIZ_LOGIC["业务逻辑服务<br/>(待开发)"]
            DATA_SERVICE["数据处理服务<br/>(待开发)"]
            TASK_SERVICE["异步任务服务<br/>(待开发)"]
        end

        %% 数据层
        subgraph DB_LAYER["🗄️ 数据层"]
            MODELS["数据模型<br/>(待开发)"]
            CRUD["CRUD操作<br/>(待开发)"]
            DATABASE["数据库<br/>(待配置)"]
        end
    end

    %% 核心业务模式
    subgraph BUSINESS_MODES["🎯 业务模式系统"]
        COORD_MODE["坐标模式<br/>显示x,y坐标"]
        COLOR_MODE["颜色模式<br/>9种颜色类型"]
        LEVEL_MODE["等级模式<br/>1-4级数据"]
        WORD_MODE["词语模式<br/>文本内容显示"]
    end

    %% 连接关系
    WORKSPACE --> FRONTEND
    WORKSPACE --> BACKEND

    %% 前端内部连接
    UI_LAYER --> STATE_LAYER
    STATE_LAYER --> LOGIC_LAYER
    LOGIC_LAYER --> DATA_LAYER

    %% 具体组件连接
    MATRIX_COMP --> MATRIX_STORE
    CONTROLS_COMP --> MATRIX_STORE
    MATRIX_STORE --> MATRIX_CORE
    MATRIX_CORE --> GROUP_DATA
    MATRIX_STORE --> PERSIST
    MATRIX_STORE --> CACHE

    %% 业务模式连接
    MATRIX_CORE --> BUSINESS_MODES
    MODE_HANDLERS --> BUSINESS_MODES

    %% 后端内部连接
    API_LAYER --> SERVICE_LAYER
    SERVICE_LAYER --> DB_LAYER
    FASTAPI_APP --> API_ROUTER
    API_ROUTER --> HEALTH_EP

    %% 前后端连接
    FRONTEND -.->|HTTP API| BACKEND
    DATA_LAYER -.->|API调用| API_LAYER

    %% 应用样式
    class MATRIX_COMP,CONTROLS_COMP,BUTTON_COMP,ICONS_COMP ui
    class MATRIX_STORE,PERSIST,CACHE frontend
    class MATRIX_CORE,MODE_HANDLERS,MATRIX_TYPES core
    class GROUP_DATA,DATA_CACHE,DATA_GEN data
    class FASTAPI_APP,API_ROUTER,HEALTH_EP api
    class BIZ_LOGIC,DATA_SERVICE,TASK_SERVICE backend
    class MODELS,CRUD,DATABASE backend
```

## 架构层次说明

### 1. 用户界面层 (UI Layer)
- **Matrix组件**: 负责33x33网格的渲染和交互
- **Controls组件**: 提供模式切换和操作控制
- **Button组件**: 统一的按钮设计系统
- **Icons组件**: 图标系统

### 2. 状态管理层 (State Management)
- **MatrixStore**: 基于Zustand的响应式状态管理
- **持久化中间件**: 支持状态持久化和恢复
- **计算属性缓存**: 性能优化的缓存机制

### 3. 业务逻辑层 (Business Logic)
- **MatrixCore**: 核心处理引擎，统一的矩阵处理逻辑
- **ModeHandlers**: 业务模式处理器，支持插件式扩展
- **MatrixTypes**: 统一的类型系统
- **业务模式**: 支持坐标、颜色、等级、词语四种模式

### 4. 数据层 (Data Layer)
- **GroupAData**: A-M组数据源管理
- **数据缓存**: 高性能缓存管理器
- **数据生成器**: 动态数据生成功能

### 5. 后端服务层 (Backend Services)
- **FastAPI应用**: 基于FastAPI的后端服务
- **API路由**: 模块化的API路由管理
- **健康检查**: 系统健康状态监控
- **API端点**: 具体的业务端点

## 数据流架构

### 数据层次结构
```
UI组件层 (Components)
    ↓
业务逻辑层 (Hooks/Managers)
    ↓
数据存储层 (Stores)
    ↓
工具函数层 (Utils)
```

### 核心数据流
1. **用户交互** → UI组件
2. **组件事件** → Hook处理
3. **业务逻辑** → 数据管理器
4. **状态更新** → Store更新
5. **UI响应** → 组件重渲染

## 性能优化策略

- **计算属性缓存**: 避免重复计算，提升渲染性能
- **状态持久化**: 支持应用状态的本地存储和恢复
- **纯组件设计**: 数据驱动的无状态组件，减少不必要的重渲染
- **类型安全**: 100% TypeScript覆盖，编译时错误检查
- **模块化架构**: 清晰的职责分离，便于维护和扩展
