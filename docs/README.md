# 项目文档结构说明

本目录包含项目的所有文档，按照以下三类结构组织：

## 📁 文档分类

### 1. 开发日志类 (`log{YYMMDD}/`)

存放项目开发过程中的详细记录，按日期组织。

**命名规范**: `log{YYMMDD}/`

- `log250104/` - 2025年1月4日的开发日志
- `log250627/` - 2025年6月27日的开发日志

**内容包括**:

- 每日开发进度记录
- 问题解决过程
- 技术决策记录
- 代码变更说明

### 2. 分析报告类 (`report/`)

存放技术分析、项目报告等正式文档。

**内容包括**:

- 技术架构分析
- 性能优化报告
- 代码质量分析
- 重构总结报告
- 部署指南
- 项目实施路线图

### 3. 文档模板示例类 (`templates/`)

存放标准化的文档格式模板。

**内容包括**:

- 开发日志模板 (`log-template.md`)
- 技术报告模板 (`report-template.md`)
- 架构文档模板 (`architecture-template.md`)

## 📋 使用指南

### 创建开发日志

1. 在根目录下创建新的日期目录：`docs/log{YYMMDD}/`
2. 使用模板 `templates/log-template.md` 创建日志文件
3. 按照模板格式填写内容

### 创建技术报告

1. 在 `docs/report/` 目录下创建新文件
2. 使用模板 `templates/report-template.md` 作为基础
3. 根据报告类型调整内容结构

### 更新架构文档

1. 使用模板 `templates/architecture-template.md`
2. 定期更新架构变更
3. 维护版本历史记录

## 🔍 文档索引

### 项目管理规范

- **[Augment User Guidelines](./USER_GUIDELINES.md)** - AI助手编码行为规范，确保代码质量和开发效率
- **[文档文件管理规范](./FILE_MANAGEMENT_GUIDELINES.md)** - docs目录统一管理规范，确保文档结构清晰

### 📊 技术报告

- **[技术报告目录](./report/README.md)** - 查看所有技术分析和优化报告
- [网格系统架构分析](./report/grid-system-architecture-analysis.md) - 核心架构设计分析
- [渲染架构重构报告](./report/rendering-architecture-refactor.md) - 性能优化重构记录

### 🔄 迁移文档

- **[迁移文档目录](./migration/README.md)** - 查看所有技术迁移记录
- [TanStack Query迁移](./migration/tanstack-query-migration.md) - 状态管理库迁移过程

### 📐 架构图表

- **[图表文档目录](./diagrams/README.md)** - 查看所有系统架构图和流程图
- [系统架构图](./diagrams/system-architecture.svg) - 整体系统架构
- [业务流程图](./diagrams/business-flow.svg) - 核心业务流程

### 📝 文档模板

- **[模板使用指南](./templates/README.md)** - 了解如何使用文档模板
- [技术报告模板](./templates/report-template.md) - 创建技术报告
- [架构文档模板](./templates/architecture-template.md) - 设计架构文档
- [开发日志模板](./templates/log-template.md) - 记录开发过程

## 📝 文档规范

### 命名规范

- **日志文件**: `log_{YYMMDD}_{HHMM}.md`
- **报告文件**: `{功能描述}_{YYMMDD}.md`
- **模板文件**: `{类型}-template.md`

### 格式规范

- 使用Markdown格式
- 统一的标题层级结构
- 包含必要的元数据（日期、作者、版本等）
- 使用emoji增强可读性

---

**文档结构版本**: v2.3
**最后更新**: 2025年7月30日
**维护人**: Augment Agent
**项目状态**: ✅ Monorepo架构完成，全栈开发就绪
