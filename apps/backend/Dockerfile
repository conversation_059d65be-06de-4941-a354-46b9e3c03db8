# Cube1 Group Backend - FastAPI Dockerfile
# 🎯 核心价值：容器化Python FastAPI应用
# 🚀 多阶段构建：优化镜像大小和安全性
# ⚡ 生产就绪：包含健康检查和安全配置

# === 构建阶段 ===
FROM python:3.11-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 配置Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 安装依赖
RUN poetry install --only=main && rm -rf $POETRY_CACHE_DIR

# === 运行阶段 ===
FROM python:3.11-slim as runtime

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制虚拟环境
ENV VIRTUAL_ENV=/app/.venv
COPY --from=builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}

# 确保使用虚拟环境
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# 复制应用代码
COPY --chown=appuser:appuser . .

# 创建必要的目录
RUN mkdir -p logs && chown -R appuser:appuser logs

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

# === 开发版本 ===
FROM runtime as development

# 切换回root用户安装开发依赖
USER root

# 从构建阶段复制完整的虚拟环境（包含开发依赖）
COPY --from=builder /app/.venv /app/.venv

# 安装开发工具
RUN apt-get update && apt-get install -y \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 切换回应用用户
USER appuser

# 开发模式启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# === 生产版本 ===
FROM runtime as production

# 设置生产环境变量
ENV ENVIRONMENT=production
ENV PYTHONOPTIMIZE=1
ENV PYTHONDONTWRITEBYTECODE=1

# 使用Gunicorn启动
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
