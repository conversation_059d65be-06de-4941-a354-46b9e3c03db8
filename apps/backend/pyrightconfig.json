{"include": ["app"], "exclude": ["**/__pycache__", "**/.pytest_cache", "**/node_modules", "**/*.pyc"], "venvPath": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs", "venv": "cube1-backend-7s4oHNYO-py3.11", "pythonVersion": "3.11", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoSearchPaths": true, "autoImportCompletions": true, "extraPaths": [".", "./app"], "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportImportCycles": "warning", "reportUnusedImport": "warning", "reportUnusedClass": "warning", "reportUnusedFunction": "warning", "reportUnusedVariable": "warning", "reportDuplicateImport": "warning", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportGeneralTypeIssues": "warning", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportInconsistentConstructor": "none", "reportOverlappingOverloads": "warning", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportInvalidStringEscapeSequence": "warning", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "warning", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportAssertAlwaysTrue": "warning", "reportSelfClsParameterName": "warning", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStubStatement": "warning", "reportIncompleteStub": "none", "reportUnsupportedDunderAll": "warning", "reportUnusedCoroutine": "warning"}