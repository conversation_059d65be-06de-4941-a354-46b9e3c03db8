# Cube1 Group - Docker Compose配置
# 🎯 核心价值：本地开发环境的完整容器化解决方案
# 🚀 服务：FastAPI后端、PostgreSQL、Redis、开发工具
# ⚡ 特性：热重载、数据持久化、网络隔离、健康检查

version: '3.8'

services:
  # === FastAPI后端服务 ===
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cube1-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://cube1_user:cube1_password@postgres:5432/cube1_db
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
    volumes:
      - .:/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cube1_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # === PostgreSQL数据库 ===
  postgres:
    image: postgres:15-alpine
    container_name: cube1-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: cube1_db
      POSTGRES_USER: cube1_user
      POSTGRES_PASSWORD: cube1_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - cube1_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cube1_user -d cube1_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # === Redis缓存服务 ===
  redis:
    image: redis:7-alpine
    container_name: cube1-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass cube1_redis_password
    volumes:
      - redis_data:/data
    networks:
      - cube1_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # === Celery Worker (异步任务) ===
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cube1-celery-worker
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://cube1_user:cube1_password@postgres:5432/cube1_db
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - .:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - cube1_network
    restart: unless-stopped

  # === Celery Beat (定时任务) ===
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cube1-celery-beat
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=postgresql+asyncpg://cube1_user:cube1_password@postgres:5432/cube1_db
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - .:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - cube1_network
    restart: unless-stopped

  # === Flower (Celery监控) ===
  flower:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cube1-flower
    command: celery -A app.tasks.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - ENVIRONMENT=development
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    networks:
      - cube1_network
    restart: unless-stopped

  # === 数据库管理工具 (可选) ===
  adminer:
    image: adminer:latest
    container_name: cube1-adminer
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      - postgres
    networks:
      - cube1_network
    restart: unless-stopped

  # === Redis管理工具 (可选) ===
  redis_commander:
    image: rediscommander/redis-commander:latest
    container_name: cube1-redis-commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:1:cube1_redis_password
    depends_on:
      - redis
    networks:
      - cube1_network
    restart: unless-stopped

# === 网络配置 ===
networks:
  cube1_network:
    driver: bridge
    name: cube1_network

# === 数据卷配置 ===
volumes:
  postgres_data:
    name: cube1_postgres_data
  redis_data:
    name: cube1_redis_data
  backend_logs:
    name: cube1_backend_logs
  celery_logs:
    name: cube1_celery_logs
