# Cube1 Group - FastAPI Backend

🎯 **现代化FastAPI后端服务**

基于FastAPI 0.116.1的高性能后端API，为Cube1 Group网格数据可视化系统提供数据服务。

## 🚀 快速开始

### 环境要求

- **Python 3.11** (必需)
- Poetry 1.0+ (依赖管理)

### 手动启动

```bash
# 1. 安装依赖
poetry install

# 2. 启动服务
poetry run uvicorn app.main:app --reload
```

### Docker启动

```bash
# 启动后端服务
docker-compose up backend
```

## 📚 API文档

启动服务后访问：

- **Swagger UI**: <http://localhost:8000/docs>
- **ReDoc**: <http://localhost:8000/redoc>
- **健康检查**: <http://localhost:8000/health>

## 🏗️ 项目结构

```text
backend/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 基础配置管理
│   ├── api/               # API路由
│   │   └── v1/
│   │       ├── router.py  # 主路由
│   │       └── endpoints/ # 具体端点
│   ├── core/              # 核心功能
│   │   ├── exceptions.py  # 异常处理
│   │   └── logging.py     # 日志配置
│   ├── models/            # 数据模型（待开发）
│   ├── services/          # 业务逻辑（待开发）
│   ├── tasks/             # 异步任务（待开发）
│   └── crud/              # CRUD操作（待开发）
├── tests/                 # 测试文件（待开发）
├── pyproject.toml        # Poetry配置
├── Dockerfile
├── docker-compose.yml
└── README.md
```

## 🛠️ 技术栈

### 核心框架

- **FastAPI 0.116.1**: 高性能Web框架
- **Pydantic 2.11.7**: 数据验证和序列化
- **Python 3.11+**: 现代Python版本

### 开发工具

- **Ruff**: 快速代码检查
- **Black**: 代码格式化
- **isort**: 导入排序
- **MyPy**: 类型检查
- **Pytest**: 测试框架

## 🔧 开发指南

### 代码质量

```bash
# 导入排序
poetry run isort .

# 代码格式化
poetry run black .

# 代码检查
poetry run ruff check .

# 类型检查
poetry run mypy .

# 运行测试
poetry run pytest

# 一键格式化和检查
poetry run isort . && poetry run black . && poetry run ruff check .
```

## 📊 API端点

### 🏠 根路径和信息

- `GET /` - 应用信息和状态
- `GET /api/v1/` - API版本信息

### 🏥 系统健康检查

- `GET /health` - 基础健康检查
- `GET /api/v1/health/` - 基础健康状态

## 🐳 Docker部署

### 开发环境

```bash
# 启动后端服务
docker-compose up backend

# 查看日志
docker-compose logs -f backend

# 停止服务
docker-compose down
```

## 🧪 测试

```bash
# 运行所有测试
poetry run pytest

# 生成覆盖率报告
poetry run pytest --cov=app --cov-report=html
```

## 📝 开发规范

### 代码风格

- 使用isort进行导入排序
- 使用Black进行代码格式化
- 使用Ruff进行代码检查
- 使用MyPy进行类型检查
- 遵循PEP 8规范

### 提交规范

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请提交Issue或联系开发团队。

---

**维护者**: Augment Agent
**最后更新**: 2025年7月30日
**状态**: ✅ 后端服务就绪，支持网格数据管理和API服务
