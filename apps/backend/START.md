# 后端启动指南

## 快速启动

```bash
# 进入后端目录
cd apps/backend

# 安装依赖
poetry install

# 启动开发服务器
poetry run uvicorn app.main:app --reload
```

## 访问地址

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **API信息**: http://localhost:8000/api/v1/

## 当前状态

✅ 基础FastAPI框架已就绪  
⏳ 等待前端需求确认后开发具体功能  
🔧 已移除所有业务逻辑代码  

## 下一步

1. 前端梳理完成后，确认前后端交互需求
2. 根据需求重新设计API接口
3. 实现具体的业务逻辑
4. 添加数据库和认证功能
