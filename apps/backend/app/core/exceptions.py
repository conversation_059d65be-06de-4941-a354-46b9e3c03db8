"""
自定义异常类 - 统一错误处理
🎯 核心价值：标准化错误处理，提供详细的错误信息和状态码
🔧 功能：应用异常、HTTP异常、业务异常、验证异常
⚡ 特性：错误码管理、多语言支持、详细错误信息
"""

from typing import Any


class AppException(Exception):
    """应用基础异常类"""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR",
        details: dict[str, Any] | None = None,
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(AppException):
    """验证异常"""

    def __init__(
        self, message: str = "数据验证失败", details: dict[str, Any] | None = None
    ):
        super().__init__(
            message=message,
            status_code=422,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class AuthenticationException(AppException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", details: dict[str, Any] | None = None):
        super().__init__(
            message=message,
            status_code=401,
            error_code="AUTHENTICATION_ERROR",
            details=details,
        )


class AuthorizationException(AppException):
    """授权异常"""

    def __init__(self, message: str = "权限不足", details: dict[str, Any] | None = None):
        super().__init__(
            message=message,
            status_code=403,
            error_code="AUTHORIZATION_ERROR",
            details=details,
        )


class NotFoundException(AppException):
    """资源未找到异常"""

    def __init__(
        self,
        message: str = "资源未找到",
        resource: str | None = None,
        resource_id: str | None = None,
    ):
        details = {}
        if resource:
            details["resource"] = resource
        if resource_id:
            details["resource_id"] = resource_id

        super().__init__(
            message=message, status_code=404, error_code="NOT_FOUND", details=details
        )


class ConflictException(AppException):
    """资源冲突异常"""

    def __init__(self, message: str = "资源冲突", details: dict[str, Any] | None = None):
        super().__init__(
            message=message,
            status_code=409,
            error_code="CONFLICT_ERROR",
            details=details,
        )


class DatabaseException(AppException):
    """数据库异常"""

    def __init__(
        self, message: str = "数据库操作失败", details: dict[str, Any] | None = None
    ):
        super().__init__(
            message=message,
            status_code=500,
            error_code="DATABASE_ERROR",
            details=details,
        )


class ExternalServiceException(AppException):
    """外部服务异常"""

    def __init__(
        self,
        message: str = "外部服务调用失败",
        service_name: str | None = None,
        details: dict[str, Any] | None = None,
    ):
        if service_name and details:
            details["service"] = service_name
        elif service_name:
            details = {"service": service_name}

        super().__init__(
            message=message,
            status_code=502,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
        )


class RateLimitException(AppException):
    """速率限制异常"""

    def __init__(self, message: str = "请求过于频繁", retry_after: int | None = None):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after

        super().__init__(
            message=message,
            status_code=429,
            error_code="RATE_LIMIT_ERROR",
            details=details,
        )


class BusinessException(AppException):
    """业务逻辑异常"""

    def __init__(
        self, message: str, business_code: str, details: dict[str, Any] | None = None
    ):
        if details:
            details["business_code"] = business_code
        else:
            details = {"business_code": business_code}

        super().__init__(
            message=message,
            status_code=400,
            error_code="BUSINESS_ERROR",
            details=details,
        )


# === 特定业务异常 ===


class UserNotFoundException(NotFoundException):
    """用户未找到异常"""

    def __init__(self, user_id: str):
        super().__init__(message="用户不存在", resource="user", resource_id=user_id)


class ProjectNotFoundException(NotFoundException):
    """项目未找到异常"""

    def __init__(self, project_id: str):
        super().__init__(message="项目不存在", resource="project", resource_id=project_id)


class DuplicateEmailException(ConflictException):
    """邮箱重复异常"""

    def __init__(self, email: str):
        super().__init__(message="邮箱已存在", details={"email": email})


class InvalidCredentialsException(AuthenticationException):
    """无效凭据异常"""

    def __init__(self) -> None:
        super().__init__(message="用户名或密码错误")


class TokenExpiredException(AuthenticationException):
    """令牌过期异常"""

    def __init__(self) -> None:
        super().__init__(message="访问令牌已过期", details={"expired": True})


class InvalidTokenException(AuthenticationException):
    """无效令牌异常"""

    def __init__(self) -> None:
        super().__init__(message="无效的访问令牌", details={"invalid": True})


# === 异常工具函数 ===


def handle_database_error(error: Exception) -> AppException:
    """处理数据库错误"""
    error_msg = str(error)

    # 常见数据库错误处理
    if "UNIQUE constraint failed" in error_msg:
        return ConflictException("数据重复，违反唯一性约束")
    elif "FOREIGN KEY constraint failed" in error_msg:
        return ValidationException("外键约束失败，关联数据不存在")
    elif "NOT NULL constraint failed" in error_msg:
        return ValidationException("必填字段不能为空")
    else:
        return DatabaseException(f"数据库操作失败: {error_msg}")


def handle_validation_error(error: Exception) -> ValidationException:
    """处理验证错误"""
    return ValidationException(
        message="数据验证失败", details={"validation_error": str(error)}
    )
