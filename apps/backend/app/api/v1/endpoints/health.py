"""
健康检查API端点 - Health Check Endpoints
🎯 核心价值：基础系统健康状态监控
🔧 功能：基础健康检查
⚡ 特性：快速响应、基础状态监控
"""

from datetime import datetime

from fastapi import APIRouter

router = APIRouter()


@router.get("/", summary="基础健康检查")
async def basic_health_check() -> dict[str, str]:
    """
    基础健康检查

    返回系统基本状态信息
    """
    return {
        "status": "healthy",
        "service": "cube1-backend",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": "development",
        "message": "基础框架运行正常，等待前端需求确认后开发具体功能",
    }
