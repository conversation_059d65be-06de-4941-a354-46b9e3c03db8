"""
API v1 主路由 - 基础路由框架
🎯 核心价值：基础API路由结构
🔧 功能：基础路由注册
⚡ 特性：等待前端需求确认后开发具体功能
"""

from fastapi import APIRouter

from app.api.v1.endpoints import health

# 创建API v1路由器
api_router = APIRouter()

# === 系统健康检查路由 ===
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["系统健康 Health"],
)


# === API信息端点 ===
@api_router.get("/", summary="API信息", tags=["API信息"])
async def api_info() -> dict[str, str | dict[str, str]]:
    """获取API版本信息"""
    return {
        "name": "Cube1 Group Backend API",
        "version": "v1",
        "description": "基础 Backend API v1 - 等待前端需求确认",
        "endpoints": {
            "health": "/health - 系统健康检查",
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc",
        },
        "status": "基础框架已就绪，等待前端需求确认后开发具体功能",
    }
