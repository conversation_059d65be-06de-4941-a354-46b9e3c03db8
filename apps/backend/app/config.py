"""
配置管理 - 基础配置
🎯 核心价值：基础应用配置
🔧 功能：基础应用配置
⚡ 特性：简化配置，等待前端需求确认
"""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppSettings(BaseSettings):
    """应用配置"""

    # 应用基本信息
    app_name: str = Field(default="Cube1 Backend", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=True, description="调试模式")

    # API配置
    api_prefix: str = Field(default="/api/v1", description="API前缀")
    docs_url: str = Field(default="/docs", description="API文档URL")
    redoc_url: str = Field(default="/redoc", description="ReDoc文档URL")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式",
    )


class Settings(BaseSettings):
    """主配置类 - 基础配置"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # 环境配置
    environment: str = Field(default="development", description="运行环境")

    # 子配置
    app: AppSettings = Field(default_factory=AppSettings)

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment.lower() in ("development", "dev")

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment.lower() in ("production", "prod")

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment.lower() in ("testing", "test")


# 全局配置实例
settings = Settings()
