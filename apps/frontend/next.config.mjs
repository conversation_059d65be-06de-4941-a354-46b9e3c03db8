import path from 'path';
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
    // 暂时移除standalone输出以解决构建问题
    // output: 'standalone',
    // 自定义源码目录
    pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
    // TypeScript配置 - 已修复所有类型错误，恢复类型检查
    typescript: {
        ignoreBuildErrors: false, // ✅ 恢复类型检查
    },
    // 添加实验性功能配置
    experimental: {
        // 启用turbopack以提升开发体验
        turbo: {
            rules: {
                '*.svg': {
                    loaders: ['@svgr/webpack'],
                    as: '*.js',
                },
            },
        },
    },
    // Next.js 14+ 默认支持 App Router
    // 重写路径以支持frontend目录结构
    webpack: (config) => {
        // 添加路径别名（与 tsconfig.json 保持一致）
        config.resolve.alias = {
            ...config.resolve.alias,
            '@': path.resolve('.'),
            '@/components': path.resolve('./components'),
            '@/lib': path.resolve('./lib'),
            '@/utils': path.resolve('./lib/utils'),
            '@/types': path.resolve('./lib/types'),
            '@/stores': path.resolve('./stores'),
            '@/features': path.resolve('./features'),
            '@/api': path.resolve('./app/api'),
            '@/app': path.resolve('./app'),
        };
        return config;
    },
};
export default nextConfig;
