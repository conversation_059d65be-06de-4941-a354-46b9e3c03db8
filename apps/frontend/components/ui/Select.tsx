/**
 * 通用下拉选择组件
 * 🎯 核心价值：统一的选择器设计体系，现代化下拉菜单
 * 📦 功能范围：所有下拉选择的基础样式和交互行为
 * 🔄 架构设计：可复用的UI组件，支持禁用选项和自定义样式
 */

'use client';

import React, { forwardRef } from 'react';

// ===== 组件属性 =====

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange' | 'size'> {
  /** 当前选中值 */
  value: string;
  /** 选项列表 */
  options: SelectOption[];
  /** 值变化回调 */
  onChange: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 选择器尺寸 */
  size?: 'sm' | 'md' | 'lg';
}

// ===== 样式配置 =====

const baseStyles = `
  w-full rounded-lg border border-gray-300
  bg-white text-gray-900
  focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
  disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
  transition-all duration-200 ease-in-out
  appearance-none cursor-pointer
  shadow-sm hover:shadow-md
`;

const sizeStyles = {
  sm: 'px-3 py-1.5 text-sm min-h-[32px]',
  md: 'px-4 py-2 text-sm min-h-[36px]',
  lg: 'px-5 py-2.5 text-base min-h-[40px]',
};

// ===== 主组件 =====

const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  value,
  options,
  onChange,
  placeholder = '请选择...',
  disabled = false,
  className = '',
  size = 'md',
  ...props
}, ref) => {
  // 处理值变化
  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(event.target.value);
  };

  // 组合最终样式
  const finalClassName = `
    ${baseStyles}
    ${sizeStyles[size]}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className="relative">
      <select
        ref={ref}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        className={finalClassName}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
            title={option.description}
          >
            {option.label}
          </option>
        ))}
      </select>

      {/* 自定义下拉箭头 */}
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>
    </div>
  );
});

Select.displayName = 'Select';

export default Select;

// ===== 导出类型 =====

export type { SelectProps };
