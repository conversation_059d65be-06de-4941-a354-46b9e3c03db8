/**
 * 图标组件集合
 * 🎯 核心价值：统一的图标设计，现代化SVG图标
 * 📦 功能范围：按钮和界面中使用的所有图标
 * 🔄 架构设计：可复用的SVG图标组件
 */

'use client';

import React from 'react';

// ===== 图标属性接口 =====

interface IconProps {
  /** 图标尺寸 */
  size?: number | string;
  /** 自定义类名 */
  className?: string;
  /** 图标颜色 */
  color?: string;
}

// ===== 菜单图标 =====

export const MenuIcon: React.FC<IconProps> = ({
  size = 20,
  className = '',
  color = 'currentColor'
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <line x1="4" y1="6" x2="20" y2="6" />
    <line x1="4" y1="12" x2="20" y2="12" />
    <line x1="4" y1="18" x2="20" y2="18" />
  </svg>
);

// ===== 关闭图标 =====

export const CloseIcon: React.FC<IconProps> = ({
  size = 20,
  className = '',
  color = 'currentColor'
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <line x1="18" y1="6" x2="6" y2="18" />
    <line x1="6" y1="6" x2="18" y2="18" />
  </svg>
);

// ===== 重置图标 =====

export const RefreshIcon: React.FC<IconProps> = ({
  size = 20,
  className = '',
  color = 'currentColor'
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <polyline points="23 4 23 10 17 10" />
    <polyline points="1 20 1 14 7 14" />
    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
  </svg>
);