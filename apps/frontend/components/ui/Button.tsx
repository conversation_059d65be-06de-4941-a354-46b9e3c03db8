/**
 * 统一按钮组件
 * 🎯 核心价值：统一的按钮设计体系，极简现代化风格
 * 📦 功能范围：所有按钮的基础样式和交互行为
 * 🔄 架构设计：可复用的UI组件，支持多种变体和尺寸
 */

'use client';

import React, { forwardRef } from 'react';

// ===== 组件属性 =====

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'icon' | 'danger';
  /** 按钮尺寸 */
  size?: 'sm' | 'md' | 'lg' | 'cell';
  /** 是否为激活状态（用于切换按钮） */
  active?: boolean;
  /** 子元素 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

// ===== 样式配置 =====

const baseStyles = `
  inline-flex items-center justify-center
  font-medium rounded-lg border
  transition-all duration-200 ease-in-out
  focus:outline-none focus:ring-2 focus:ring-offset-2
  disabled:opacity-50 disabled:cursor-not-allowed
  shadow-sm hover:shadow-md
  select-none
`;

const variantStyles = {
  primary: {
    default: `
      bg-blue-500 text-white border-blue-500
      hover:bg-blue-600 hover:border-blue-600
      focus:ring-blue-500
      active:bg-blue-700
    `,
    active: `
      bg-blue-600 text-white border-blue-600
      hover:bg-blue-700 hover:border-blue-700
      focus:ring-blue-500
      shadow-md
    `
  },
  secondary: {
    default: `
      bg-white text-gray-700 border-gray-300
      hover:bg-gray-50 hover:border-gray-400
      focus:ring-gray-500
      active:bg-gray-100
    `,
    active: `
      bg-blue-50 text-blue-700 border-blue-300
      hover:bg-blue-100 hover:border-blue-400
      focus:ring-blue-500
      shadow-md
    `
  },
  icon: {
    default: `
      bg-white text-gray-600 border-gray-200
      hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700
      focus:ring-gray-500
      active:bg-gray-100
      hover:transform hover:-translate-y-0.5
      relative z-10
    `,
    active: `
      bg-gray-100 text-gray-700 border-gray-300
      hover:bg-gray-200 hover:border-gray-400
      focus:ring-gray-500
      shadow-md
      relative z-10
    `
  },
  danger: {
    default: `
      bg-red-500 text-white border-red-500
      hover:bg-red-600 hover:border-red-600
      focus:ring-red-500
      active:bg-red-700
    `,
    active: `
      bg-red-600 text-white border-red-600
      hover:bg-red-700 hover:border-red-700
      focus:ring-red-500
      shadow-md
    `
  }
};

const sizeStyles = {
  sm: 'px-3 py-1.5 text-sm min-h-[32px]',
  md: 'px-4 py-2 text-sm min-h-[36px]',
  lg: 'px-5 py-2.5 text-base min-h-[40px]',
  cell: 'px-2 py-1 text-xs min-h-[33px]'
};

// 图标按钮的特殊尺寸（正方形）
const iconSizeStyles = {
  sm: 'w-8 h-8 p-1.5',
  md: 'w-10 h-10 p-2',
  lg: 'w-12 h-12 p-2.5',
  cell: 'w-[33px] h-[33px] p-1'
};

// ===== 主组件 =====

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  variant = 'secondary',
  size = 'md',
  active = false,
  children,
  className = '',
  disabled = false,
  ...props
}, ref) => {
  // 获取变体样式
  const variantStyle = variantStyles[variant][active ? 'active' : 'default'];

  // 获取尺寸样式
  const sizeStyle = variant === 'icon'
    ? iconSizeStyles[size]
    : sizeStyles[size];

  // 组合最终样式
  const finalClassName = `
    ${baseStyles}
    ${variantStyle}
    ${sizeStyle}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <button
      ref={ref}
      className={finalClassName}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;

// ===== 导出类型 =====

export type { ButtonProps };
