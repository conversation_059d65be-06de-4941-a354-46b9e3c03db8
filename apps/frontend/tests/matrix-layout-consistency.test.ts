/**
 * Matrix布局一致性测试
 * 验证默认模式和颜色模式的容器设定是否一致
 */

import { render } from '@testing-library/react';
import Matrix from '@/components/Matrix';
import { useMatrixStore } from '@/core/matrix/MatrixStore';

// Mock MatrixStore
jest.mock('@/core/matrix/MatrixStore', () => ({
  useMatrixStore: jest.fn(),
}));

const mockUseMatrixStore = useMatrixStore as jest.MockedFunction<typeof useMatrixStore>;

describe('Matrix布局一致性测试', () => {
  const mockStoreData = {
    initializeMatrix: jest.fn(),
    selectCell: jest.fn(),
    hoverCell: jest.fn(),
    focusCell: jest.fn(),
    getCellRenderData: jest.fn(() => ({
      content: '',
      style: { backgroundColor: '#ffffff', color: '#000000' },
      className: 'matrix-cell',
      isInteractive: true,
    })),
  };

  const mockMatrixData = {
    cells: new Map([
      ['0,0', { x: 0, y: 0, level: 1, isActive: true, isSelected: false, isHovered: false }],
      ['1,1', { x: 1, y: 1, level: 2, isActive: true, isSelected: false, isHovered: false }],
    ]),
    selectedCells: new Set(),
    hoveredCell: null,
    focusedCell: null,
  };

  beforeEach(() => {
    mockUseMatrixStore.mockReturnValue({
      ...mockStoreData,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('容器padding一致性', () => {
    it('默认模式应该使用统一的容器padding', () => {
      const { container } = render(
        <Matrix 
          configOverride={{ mainMode: 'default' }}
          data-testid="matrix-default"
        />
      );

      const matrixContainer = container.querySelector('.matrix-container');
      expect(matrixContainer).toHaveStyle('padding: 6px');
    });

    it('颜色模式应该使用相同的容器padding', () => {
      const { container } = render(
        <Matrix 
          configOverride={{ mainMode: 'color' }}
          data-testid="matrix-color"
        />
      );

      const matrixContainer = container.querySelector('.matrix-container');
      expect(matrixContainer).toHaveStyle('padding: 6px');
    });
  });

  describe('容器尺寸一致性', () => {
    it('默认模式和颜色模式应该使用相同的容器尺寸', () => {
      const { container: defaultContainer } = render(
        <Matrix configOverride={{ mainMode: 'default' }} />
      );
      
      const { container: colorContainer } = render(
        <Matrix configOverride={{ mainMode: 'color' }} />
      );

      const defaultMatrixContainer = defaultContainer.querySelector('.matrix-container');
      const colorMatrixContainer = colorContainer.querySelector('.matrix-container');

      // 验证容器尺寸一致
      expect(defaultMatrixContainer).toHaveStyle('width: 1134px'); // 1122 + 12
      expect(colorMatrixContainer).toHaveStyle('width: 1134px');
      expect(defaultMatrixContainer).toHaveStyle('height: 1134px');
      expect(colorMatrixContainer).toHaveStyle('height: 1134px');
    });
  });

  describe('视口样式一致性', () => {
    it('默认模式和颜色模式的视口样式应该一致', () => {
      const { container: defaultContainer } = render(
        <Matrix configOverride={{ mainMode: 'default' }} />
      );
      
      const { container: colorContainer } = render(
        <Matrix configOverride={{ mainMode: 'color' }} />
      );

      const defaultViewport = defaultContainer.querySelector('.matrix-viewport');
      const colorViewport = colorContainer.querySelector('.matrix-viewport');

      // 验证视口最大尺寸一致
      expect(defaultViewport).toHaveStyle('max-width: 1134px');
      expect(colorViewport).toHaveStyle('max-width: 1134px');
      expect(defaultViewport).toHaveStyle('max-height: 1134px');
      expect(colorViewport).toHaveStyle('max-height: 1134px');
    });
  });
});
