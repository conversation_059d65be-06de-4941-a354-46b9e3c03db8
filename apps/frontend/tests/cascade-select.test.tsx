/**
 * 联动选择器测试
 * 测试新的模式管理系统
 */

import CascadeSelect from '@/components/ui/CascadeSelect';
import type { DataAvailability } from '@/core/matrix/MatrixTypes';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

// 模拟数据可用性
const mockDataAvailability: DataAvailability = {
  hasCoordinateData: true,
  hasLevelData: true,
  hasMappingData: true,
  hasWordData: false,
};

describe('CascadeSelect', () => {
  const mockOnModeChange = jest.fn();

  beforeEach(() => {
    mockOnModeChange.mockClear();
  });

  it('应该正确渲染默认状态', () => {
    render(
      <CascadeSelect
        mainMode="default"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={mockDataAvailability}
      />
    );

    // 检查主模式选择器
    expect(screen.getByDisplayValue('默认')).toBeInTheDocument();

    // 检查内容模式选择器
    expect(screen.getByDisplayValue('空白')).toBeInTheDocument();
  });

  it('应该在主模式切换时更新可用选项', () => {
    render(
      <CascadeSelect
        mainMode="default"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={mockDataAvailability}
      />
    );

    // 切换到颜色模式
    const mainModeSelect = screen.getByDisplayValue('默认');
    fireEvent.change(mainModeSelect, { target: { value: 'color' } });

    // 验证回调被调用
    expect(mockOnModeChange).toHaveBeenCalledWith('color', 'blank');
  });

  it('应该根据数据可用性禁用选项', () => {
    const limitedDataAvailability: DataAvailability = {
      hasCoordinateData: true,
      hasLevelData: false,
      hasMappingData: false,
      hasWordData: false,
    };

    render(
      <CascadeSelect
        mainMode="color"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={limitedDataAvailability}
      />
    );

    // 检查内容模式选择器中的选项
    const contentModeSelect = screen.getByDisplayValue('空白');
    const options = contentModeSelect.querySelectorAll('option');

    // 等级选项应该被禁用
    const levelOption = Array.from(options).find(option =>
      option.textContent?.includes('等级')
    );
    expect(levelOption).toHaveAttribute('disabled');
  });

  it('应该在颜色模式下显示更多选项', () => {
    render(
      <CascadeSelect
        mainMode="color"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={mockDataAvailability}
      />
    );

    const contentModeSelect = screen.getByDisplayValue('空白');
    const options = contentModeSelect.querySelectorAll('option');

    // 颜色模式下应该有6个选项（空白、索引、坐标、等级、映射、词语）
    expect(options).toHaveLength(6);
  });

  it('应该在默认模式下只显示基础选项', () => {
    render(
      <CascadeSelect
        mainMode="default"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={mockDataAvailability}
      />
    );

    const contentModeSelect = screen.getByDisplayValue('空白');
    const options = contentModeSelect.querySelectorAll('option');

    // 默认模式下应该只有3个选项（空白、索引、坐标）
    expect(options).toHaveLength(3);
  });

  it('应该正确处理内容模式切换', () => {
    render(
      <CascadeSelect
        mainMode="default"
        contentMode="blank"
        onModeChange={mockOnModeChange}
        dataAvailability={mockDataAvailability}
      />
    );

    // 切换内容模式
    const contentModeSelect = screen.getByDisplayValue('空白');
    fireEvent.change(contentModeSelect, { target: { value: 'coordinate' } });

    // 验证回调被调用
    expect(mockOnModeChange).toHaveBeenCalledWith('default', 'coordinate');
  });

  it('应该在坐标和索引模式下使用小字体', () => {
    // 这个测试需要在实际的MatrixCore渲染测试中验证
    // 因为字体大小是在renderCellByMode函数中设置的
    expect(true).toBe(true); // 占位测试
  });
});
