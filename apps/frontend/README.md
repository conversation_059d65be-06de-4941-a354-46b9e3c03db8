# Cube1 Group - Next.js Frontend

🎯 **现代化网格数据可视化系统的前端实现**

基于Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3的现代化前端架构，已完成全面重构，采用核心引擎架构提供高性能的33×33网格渲染和交互功能。

## 🚀 快速开始

### 环境要求

- Node.js 18+
- pnpm 8.0.0+ (推荐)
- 现代浏览器支持

### 一键启动

```bash
# 进入前端目录
cd apps/frontend

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 访问应用
# http://localhost:4096
```

### 开发环境设置

```bash
# 完整开发环境设置
pnpm run dev:setup

# 启动开发服务器
pnpm run dev
```

## 🏗️ 项目结构

```
apps/frontend/
├── app/                    # Next.js App Router
│   ├── page.tsx           # 主应用页面
│   ├── layout.tsx         # 全局布局
│   └── favicon.ico        # 网站图标
├── components/            # 核心UI组件
│   ├── Matrix.tsx         # 矩阵主组件
│   └── Controls.tsx       # 控制面板组件
├── core/                  # 🚀 核心引擎架构
│   ├── matrix/            # 矩阵核心引擎
│   │   ├── MatrixCore.ts  # 矩阵处理引擎
│   │   ├── MatrixStore.ts # 统一状态管理
│   │   └── MatrixTypes.ts # 类型定义系统
│   └── data/              # 数据层
│       └── GroupAData.ts  # 优化数据集
├── styles/                # 样式文件
│   └── globals.css        # 全局样式
└── public/                # 静态资源
    └── favicon.svg        # SVG图标
```

## 🛠️ 技术栈

### 核心框架

- **Next.js 15.1.0** - React全栈框架，App Router
- **React 18.3.1** - UI库，组件化开发
- **TypeScript 5.8.3** - 严格类型安全

### 核心依赖

- **Tailwind CSS 3.4.17** - 原子化CSS框架
- **Zustand 5.0.6** - 轻量级状态管理
- **Prisma 6.11.1** - ORM和数据库工具
- **Playwright 1.54.0** - E2E测试框架

## 📦 可用命令

### 开发命令

```bash
pnpm run dev              # 启动开发服务器
pnpm run build            # 构建生产版本
pnpm run start            # 启动生产服务器
pnpm run lint             # 代码检查
pnpm run type-check       # TypeScript类型检查
```

### 测试命令

```bash
# 注意：测试框架已配置但测试脚本需要添加
# Vitest 3.2.4 - 单元测试框架
# Playwright 1.54.0 - E2E测试框架
# 测试目录配置在 playwright.config.ts 中
```

### 数据库命令

```bash
pnpm run db:generate      # 生成Prisma客户端
pnpm run db:push          # 同步数据库结构
pnpm run db:studio        # 打开数据库管理界面
pnpm run db:migrate       # 运行数据库迁移
pnpm run db:seed          # 运行数据种子
pnpm run db:reset         # 重置数据库
```

### 工具命令

```bash
pnpm run format           # 代码格式化
pnpm run format:check     # 检查代码格式
pnpm run quality:check    # 代码质量检查
pnpm run quality:report   # 生成质量报告
pnpm run demo             # 一键演示启动
pnpm run analyze          # 构建分析
pnpm run pre-commit       # 提交前检查
pnpm run ci               # CI检查
pnpm run cache:clear      # 清除缓存
pnpm run api:generate     # 生成API类型
pnpm run env:setup        # 环境设置
pnpm run env:dev          # 开发环境配置
```

## 🎯 核心功能

### � 架构重构亮点

- **核心引擎架构** - 基于MatrixCore的统一处理引擎
- **状态管理重构** - Zustand + Immer的响应式状态管理
- **组件化重构** - 从巨型文件拆分为专业组件
- **类型安全** - 完整的TypeScript类型系统

### �🖥️ 网格系统

- **33x33网格矩阵** - 1089个可交互单元格
- **高性能渲染** - 数据驱动的纯组件架构
- **业务模式切换** - 坐标模式、数据模式等多种业务场景

### 🎨 数据可视化

- **8色彩系统** - 红、青、黄、紫、橙、绿、蓝、粉
- **4层级架构** - Level 1-4 层次化数据
- **实时交互** - 悬停、选择、批量操作

## 🔧 开发指南

### 核心架构

- **MatrixCore** - 统一的矩阵处理引擎
- **MatrixStore** - 基于Zustand的状态管理
- **MatrixTypes** - 完整的TypeScript类型系统
- **纯组件设计** - 数据驱动的无状态组件

### 组件开发

- 使用函数组件 + React.memo优化
- 完全无状态组件，所有逻辑通过props注入
- 遵循单一职责原则
- 使用Tailwind CSS样式

### 类型安全

- 100% TypeScript覆盖
- 严格模式配置
- 共享类型定义
- Zod运行时验证

### 测试策略

- **Vitest 3.2.4** - 单元测试框架（已配置）
- **Playwright 1.54.0** - E2E测试框架（已配置）
- 测试目录配置：`./tests/e2e`（需要创建）
- 建议添加测试脚本到package.json

## 📚 相关文档

- [Features架构说明](./features/README.md) - 业务功能模块架构
- [Playwright配置](./playwright.config.ts) - E2E测试配置
- [Prisma Schema](./prisma/schema.prisma) - 数据库模式
- [TypeScript配置](./tsconfig.json) - 类型配置
- [Tailwind配置](./tailwind.config.ts) - 样式配置

## 🚀 部署

### 开发环境

```bash
pnpm run dev:setup        # 环境设置
pnpm run dev:full         # 完整开发环境启动
pnpm run dev              # 启动开发服务器
pnpm run dev:clean        # 清除缓存后启动
```

### 生产环境

```bash
pnpm run build:prod       # 生产构建
pnpm run env:prod         # 生产环境配置
pnpm run deploy:vercel    # 部署到Vercel
pnpm run deploy:preview   # 预览部署
pnpm run deploy:production # 生产部署
```

## 🤝 贡献指南

1. 遵循代码规范和最佳实践
2. 编写测试覆盖新功能
3. 更新相关文档
4. 提交前运行质量检查

---

**维护者**: Augment Agent
**最后更新**: 2025年7月30日
**技术支持**: 查看项目根目录README.md获取完整信息
