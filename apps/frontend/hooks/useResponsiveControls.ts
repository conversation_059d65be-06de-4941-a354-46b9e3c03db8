/**
 * 响应式控制面板Hook
 * 🎯 核心价值：智能的响应式控制面板显示逻辑
 * 📦 功能范围：窗口尺寸监听、自动显示/隐藏控制面板
 * 🔄 架构设计：基于窗口尺寸的自动化UI控制
 */

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// ===== 配置常量 =====

/** 基础响应式配置 */
const BASE_RESPONSIVE_CONFIG = {
  /** 矩阵基础尺寸 */
  MATRIX_BASE_SIZE: 1122, // 33 * 34px
  /** 统一容器扩展 */
  CONTAINER_EXPANSION: 12, // 四边扩展：6px * 2 = 12px，统一为两种模式预留空间
  /** 控制面板宽度 */
  CONTROLS_WIDTH: 320, // w-80 = 320px
  /** 最小边距 */
  MIN_MARGIN: 20, // 左右各16px边距
} as const;

/** 动态响应式配置 */
const getResponsiveConfig = () => {
  // 统一容器尺寸：避免模式切换时的位移，默认模式和颜色模式都使用相同的容器尺寸
  const MATRIX_ACTUAL_SIZE = BASE_RESPONSIVE_CONFIG.MATRIX_BASE_SIZE + BASE_RESPONSIVE_CONFIG.CONTAINER_EXPANSION;

  return {
    ...BASE_RESPONSIVE_CONFIG,
    MATRIX_ACTUAL_SIZE,
    AUTO_HIDE_BREAKPOINT: MATRIX_ACTUAL_SIZE + BASE_RESPONSIVE_CONFIG.CONTROLS_WIDTH + BASE_RESPONSIVE_CONFIG.MIN_MARGIN
  };
};

// ===== Hook接口 =====

interface UseResponsiveControlsReturn {
  /** 当前窗口宽度 */
  windowWidth: number;
  /** 当前窗口高度 */
  windowHeight: number;
  /** 是否应该悬浮显示控制面板 */
  shouldFloat: boolean;
  /** 是否为移动设备 */
  isMobile: boolean;
  /** 是否为平板设备 */
  isTablet: boolean;
  /** 是否为桌面设备 */
  isDesktop: boolean;
  /** 控制面板是否可见 */
  controlsVisible: boolean;
  /** 设置控制面板可见性 */
  setControlsVisible: (visible: boolean) => void;
  /** 切换控制面板可见性 */
  toggleControls: () => void;
  /** 控制面板显示模式 */
  displayMode: 'normal' | 'floating' | 'hidden';
}

// ===== 主Hook =====

export const useResponsiveControls = (): UseResponsiveControlsReturn => {
  // 简化的状态管理
  const [windowWidth, setWindowWidth] = useState(0);
  const [windowHeight, setWindowHeight] = useState(0);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [userControlled, setUserControlled] = useState(false);

  // 获取统一的响应式配置
  const RESPONSIVE_CONFIG = getResponsiveConfig();

  // 简化的防抖机制
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastWindowWidthRef = useRef<number>(0);

  // 确保客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 窗口尺寸监听
  useEffect(() => {
    if (!isClient) return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setWindowWidth(width);
      setWindowHeight(height);
    };

    // 初始化
    handleResize();

    // 添加监听器
    window.addEventListener('resize', handleResize);

    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isClient]);

  // 简化的用户操作处理函数
  const performUserAction = useCallback((action: () => void, actionName: string) => {
    console.log(`🎯 执行用户操作: ${actionName}`, {
      windowWidth,
      currentVisible: controlsVisible,
      userControlled
    });

    // 清除之前的防抖定时器
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // 标记为用户控制并执行操作
    setUserControlled(true);
    action();

    // 300ms后允许自动响应式调整
    debounceTimeoutRef.current = setTimeout(() => {
      console.log(`🔓 防抖结束: ${actionName}`);
    }, 300);
  }, [windowWidth, controlsVisible, userControlled]);

  // 简化的响应式控制面板可见性管理
  useEffect(() => {
    if (windowWidth === 0) return; // 避免初始化时的误触发

    const isSmallWindow = windowWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;
    const lastWidth = lastWindowWidthRef.current;
    const wasSmallWindow = lastWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;

    // 检查是否跨越了断点
    const crossedBreakpoint = (isSmallWindow !== wasSmallWindow) && lastWidth > 0;

    if (crossedBreakpoint) {
      console.log('🔄 跨越断点，重置用户控制状态', {
        lastWidth,
        currentWidth: windowWidth,
        wasSmall: wasSmallWindow,
        isSmall: isSmallWindow
      });
      setUserControlled(false); // 跨越断点时重置用户控制状态
    }

    // 更新上次窗口宽度
    lastWindowWidthRef.current = windowWidth;

    console.log('📱 响应式逻辑检查', {
      windowWidth,
      isSmallWindow,
      controlsVisible,
      userControlled,
      crossedBreakpoint,
      breakpoint: RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT
    });

    // 当窗口变小时，只有在用户没有手动控制的情况下才自动隐藏控制面板
    if (isSmallWindow && controlsVisible && !userControlled) {
      console.log('📱 窗口变小，自动隐藏控制面板');
      setControlsVisible(false);
    }
    // 当窗口变大时，只有在用户没有手动控制的情况下才自动显示控制面板
    else if (!isSmallWindow && !controlsVisible && !userControlled) {
      console.log('🖥️ 窗口变大，自动显示控制面板');
      setControlsVisible(true);
    }
  }, [windowWidth, controlsVisible, userControlled]);

  // 计算响应式状态
  const shouldFloat = windowWidth > 0 && windowWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;
  const isMobile = windowWidth > 0 && windowWidth < 768;
  const isTablet = windowWidth >= 768 && windowWidth < 1024;
  const isDesktop = windowWidth >= 1024;

  // 计算显示模式
  const displayMode: 'normal' | 'floating' | 'hidden' =
    shouldFloat ? (controlsVisible ? 'floating' : 'hidden') :
      (controlsVisible ? 'normal' : 'hidden');

  // 切换控制面板可见性
  const toggleControls = useCallback(() => {
    performUserAction(() => {
      setControlsVisible(prev => !prev);
    }, 'toggleControls');
  }, [performUserAction]);

  // 设置控制面板可见性（包装原始setter以标记用户控制）
  const setControlsVisibleWithUserControl = useCallback((visible: boolean) => {
    performUserAction(() => {
      setControlsVisible(visible);
    }, `setControlsVisible(${visible})`);
  }, [performUserAction]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    windowWidth,
    windowHeight,
    shouldFloat,
    isMobile,
    isTablet,
    isDesktop,
    controlsVisible,
    setControlsVisible: setControlsVisibleWithUserControl,
    toggleControls,
    displayMode,
  };
};

// ===== 导出配置 =====

export { BASE_RESPONSIVE_CONFIG, getResponsiveConfig };

// ===== 类型导出 =====

export type { UseResponsiveControlsReturn };

