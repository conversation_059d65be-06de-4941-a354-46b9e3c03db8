/**
 * 点击外部检测 Hook
 * 🎯 核心价值：统一的点击外部检测逻辑，简化组件中的事件处理
 * 📦 功能范围：检测点击是否在指定元素外部，支持排除特定元素
 * 🔄 架构设计：可复用的自定义 Hook，减少重复代码
 */

'use client';

import { useEffect, useRef } from 'react';

// ===== Hook 接口 =====

interface UseClickOutsideOptions {
  /** 是否启用点击外部检测 */
  enabled?: boolean;
  /** 排除的选择器列表，点击这些元素不会触发回调 */
  excludeSelectors?: string[];
}

// ===== 主 Hook =====

/**
 * 点击外部检测 Hook
 * @param callback 点击外部时的回调函数
 * @param options 配置选项
 * @returns ref 对象，需要绑定到要检测的元素上
 */
export const useClickOutside = <T extends HTMLElement = HTMLDivElement>(
  callback: () => void,
  options: UseClickOutsideOptions = {}
) => {
  const { enabled = true, excludeSelectors = [] } = options;
  const ref = useRef<T>(null);

  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // 检查点击是否在目标元素内部
      if (ref.current && !ref.current.contains(target)) {
        // 检查是否点击了排除的元素
        const isExcluded = excludeSelectors.some(selector => {
          const excludedElement = document.querySelector(selector);
          return excludedElement && excludedElement.contains(target);
        });

        if (!isExcluded) {
          callback();
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [enabled, callback, excludeSelectors]);

  return ref;
};

// ===== 导出类型 =====

export type { UseClickOutsideOptions };
