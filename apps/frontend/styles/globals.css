@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 - 简化版本 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 修复水合不匹配：明确设置 overscroll 行为 */
  overscroll-behavior-x: none;
}

/* 矩阵专用样式 */

/* 矩阵视口（外层滚动容器） */
.matrix-viewport {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.matrix-viewport::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.matrix-viewport::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.matrix-viewport::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.matrix-viewport::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* 矩阵容器（内层矩阵） */
.matrix-container {
  will-change: transform;
  contain: layout style paint;
}

/* 矩阵单元格 - 现代化极简设计 */
.matrix-cell {
  transition: all 0.2s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
  border-radius: 6px;
  /* 统一圆角 */
  font-weight: bold;
  /* 统一加粗 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* 统一阴影 */
}

.matrix-cell:hover {
  transform: translateY(-1px);
  /* 轻微上浮效果 */
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  /* 增强阴影 */
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 统一的字体样式 - 极简化 */
.matrix-cell.coordinate-mode,
.matrix-cell.color-mode,
.matrix-cell.level-mode,
.matrix-cell.word-mode {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: bold;
  line-height: 1.2;
}