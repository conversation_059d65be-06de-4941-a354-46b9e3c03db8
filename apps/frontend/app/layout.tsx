import type { Metadata } from 'next';
import '../styles/globals.css';

export const metadata: Metadata = {
    title: 'Cube1 Matrix - 数据驱动的33×33矩阵系统',
    description: '基于矩阵基础结构渲染和数据驱动视图的激进重构版本',
    metadataBase: new URL('http://localhost:4096/'),
    openGraph: {
        title: 'Cube1 Matrix',
        description: '数据驱动的33×33矩阵系统',
        url: 'http://localhost:4096',
        siteName: 'Cube1 Matrix',
        type: 'website',
    },
};

export default function RootLayout({
    children
}: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html
            lang="zh-CN"
            style={{ overscrollBehaviorX: 'none' }}
            suppressHydrationWarning
        >
            <body
                className="antialiased"
                style={{ overscrollBehaviorX: 'none' }}
                suppressHydrationWarning
            >
                {children}
            </body>
        </html>
    );
}
