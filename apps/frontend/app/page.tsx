/**
 * 主应用页面 - 重构版本
 * 🎯 核心价值：数据驱动的矩阵应用，最小化架构，零冗余
 * 📦 功能范围：矩阵渲染、控制面板、模式切换
 * 🔄 架构设计：基于新的核心架构，完全数据驱动的视图
 */

'use client';

import Controls from '@/components/Controls';
import Matrix from '@/components/Matrix';
import Button from '@/components/ui/Button';
import { CloseIcon, MenuIcon } from '@/components/ui/Icons';
import { useResponsiveControls } from '@/hooks/useResponsiveControls';
import { useCallback, useEffect, useRef, useState } from 'react';


// ===== 主应用组件 =====

export default function HomePage() {
  // 响应式控制面板逻辑
  const {
    controlsVisible,
    setControlsVisible,
    toggleControls,
    displayMode
  } = useResponsiveControls();

  const [isClient, setIsClient] = useState(false);

  // 控制面板引用，用于点击外部检测
  const floatingControlsRef = useRef<HTMLDivElement>(null);

  // 确保客户端渲染一致性，避免 hydration 错误
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 点击外部自动隐藏控制面板（仅在floating模式下）
  useEffect(() => {
    if (!isClient || displayMode !== 'floating' || !controlsVisible) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // 检查点击是否在控制面板外部
      if (floatingControlsRef.current && !floatingControlsRef.current.contains(target)) {
        // 检查点击是否在菜单按钮上（避免冲突）
        const menuButton = document.querySelector('[title="显示控制面板"]');
        if (menuButton && menuButton.contains(target)) {
          return;
        }

        console.log('👆 点击外部区域，隐藏控制面板');
        // 使用统一的状态管理
        setControlsVisible(false);
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isClient, displayMode, controlsVisible, setControlsVisible]);

  // 统一的事件处理 - 简化版本
  const handleEvent = useCallback((type: string, data: any) => {
    console.log(`${type}:`, data);
  }, []);

  // 处理控制面板隐藏按钮点击
  const handleHideControls = useCallback((event: React.MouseEvent) => {
    console.log('🔘 关闭按钮被点击', {
      windowWidth: window.innerWidth,
      displayMode,
      controlsVisible,
      eventType: event.type,
      target: event.target
    });

    event.preventDefault(); // 防止默认行为
    event.stopPropagation(); // 防止事件冒泡

    // 使用统一的状态管理，这会自动处理用户操作锁定
    setControlsVisible(false);
  }, [setControlsVisible, displayMode, controlsVisible]);

  // ===== 控制面板内容组件 =====

  const ControlsPanel: React.FC<{
    mode: 'normal' | 'floating';
    onHide: (event: React.MouseEvent) => void;
  }> = ({ mode, onHide }) => (
    <>
      <div className={`${mode === 'floating' ? 'p-3' : 'p-4'} border-b border-gray-200 relative z-20`}>
        <div className="flex items-center justify-between">
          <h2 className={`${mode === 'floating' ? 'text-base' : 'text-lg'} font-semibold text-gray-800`}>
            矩阵系统
          </h2>
          <Button
            variant="icon"
            size="cell"
            onClick={onHide}
            title="隐藏控制面板"
            className={`relative ${mode === 'floating' ? 'z-40' : 'z-30'}`}
          >
            <CloseIcon size={14} />
          </Button>
        </div>
      </div>

      <div className={mode === 'floating' ? 'p-3' : 'p-4'}>
        <Controls
          onModeChange={(mode) => handleEvent('Mode changed to', mode)}
          onReset={() => handleEvent('Matrix reset', null)}
        />
      </div>
    </>
  );




  // 在客户端渲染完成前显示加载状态，避免 hydration 错误
  if (!isClient) {
    return (
      <div className="app-container h-screen flex bg-gray-100 items-center justify-center">
        <div className="text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="app-container min-h-screen flex">
      {/* 主矩阵区域 - 响应式显示，保持1:1比例 */}
      <div className="matrix-area flex-1 flex items-center justify-center p-3" >
        {/* 矩阵容器 - 响应式尺寸，保持1:1比例， */}
        <div className="matrix-container w-full h-full max-w-full max-h-full flex items-center justify-center">
          <Matrix
            onCellClick={(coord) => handleEvent('Cell clicked', coord)}
            onCellDoubleClick={(coord) => handleEvent('Cell double-clicked', coord)}
            onModeChange={(mode) => handleEvent('Mode changed to', mode)}
            className=""
            style={{
              width: '100%',
              height: '100%',
              maxWidth: 'min(100vw - 1rem, 100vh - 1rem)',
              maxHeight: 'min(100vw - 1rem, 100vh - 1rem)',
              aspectRatio: '1 / 1'
            }}
          />
        </div>
      </div>

      {/* 控制面板 - 响应式显示 */}
      {controlsVisible && displayMode === 'normal' && (
        <div className="controls-sidebar flex w-80 bg-white border-l border-gray-200 flex-shrink-0 flex-col">
          <ControlsPanel mode="normal" onHide={handleHideControls} />
        </div>
      )}

      {/* 悬浮控制面板 */}
      {controlsVisible && displayMode === 'floating' && (
        <div
          ref={floatingControlsRef}
          className="fixed top-4 right-4 z-20 w-72 bg-white border border-gray-200 rounded-lg shadow-lg"
        >
          <ControlsPanel mode="floating" onHide={handleHideControls} />
        </div>
      )}



      {/* 菜单图标按钮（当面板隐藏时） */}
      {displayMode === 'hidden' && (
        <div className="fixed top-4 right-4 z-10">
          <Button
            variant="icon"
            size="cell"
            onClick={toggleControls}
            title="显示控制面板"
          >
            <MenuIcon size={18} />
          </Button>
        </div>
      )}

      {/* 应用专用样式 */}
      <style jsx global>{`
        .app-container:focus {
          outline: none;
        }

        /* 极简化设计 - 去除不必要的视觉分割 */
        .matrix-area {
          background: transparent;
        }

        /* 确保矩阵响应式显示，保持1:1长宽比 */
        .matrix-container {
          display: flex;
          align-items: center;
          justify-content: center;
          aspect-ratio: 1 / 1;
          max-width: min(100vw - 2rem, 100vh - 2rem);
          max-height: min(100vw - 2rem, 100vh - 2rem);
        }

        /* 确保矩阵在大屏幕下完整显示 */
        @media (min-width: 1200px) and (min-height: 1200px) {
          .matrix-area {
            padding: 12px;
          }
        }
      `}</style>
    </div>
  );
}