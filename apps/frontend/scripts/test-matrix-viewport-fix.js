/**
 * 测试matrix-viewport渲染偏移修复
 * 验证单元格位置计算是否正确
 */

// 模拟修复前后的位置计算
function testPositionCalculation() {
  console.log('🧪 测试matrix-viewport位置计算修复');
  console.log('=====================================');

  const MATRIX_SIZE = 33;
  const CELL_SPACING = 34;
  
  // 测试场景
  const testCases = [
    {
      name: '非颜色模式',
      isColorMode: false,
      isLevel1: false,
      expectedPadding: 0,
      expectedCenterOffset: 0
    },
    {
      name: '颜色模式 - 普通格子',
      isColorMode: true,
      isLevel1: false,
      expectedPadding: 6,
      expectedCenterOffset: 0
    },
    {
      name: '颜色模式 - 1级格子',
      isColorMode: true,
      isLevel1: true,
      expectedPadding: 6,
      expectedCenterOffset: -3
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📋 测试场景: ${testCase.name}`);
    console.log('-----------------------------------');
    
    const containerPadding = testCase.isColorMode ? 6 : 0;
    const centerOffset = (testCase.isColorMode && testCase.isLevel1) ? -3 : 0;
    
    console.log(`容器padding: ${containerPadding}px`);
    console.log(`中心偏移: ${centerOffset}px`);
    
    // 测试几个关键位置
    const testPositions = [
      { x: 0, y: 0, desc: '左上角' },
      { x: 16, y: 16, desc: '中心点' },
      { x: 32, y: 32, desc: '右下角' }
    ];
    
    testPositions.forEach(pos => {
      // 修复前的计算
      const oldLeft = pos.x * CELL_SPACING + centerOffset;
      const oldTop = pos.y * CELL_SPACING + centerOffset;
      
      // 修复后的计算
      const newLeft = pos.x * CELL_SPACING + centerOffset + containerPadding;
      const newTop = pos.y * CELL_SPACING + centerOffset + containerPadding;
      
      console.log(`  ${pos.desc}(${pos.x},${pos.y}):`);
      console.log(`    修复前: left=${oldLeft}px, top=${oldTop}px`);
      console.log(`    修复后: left=${newLeft}px, top=${newTop}px`);
      console.log(`    偏移量: Δx=${newLeft - oldLeft}px, Δy=${newTop - oldTop}px`);
    });
  });
  
  console.log('\n✅ 修复验证总结:');
  console.log('- 非颜色模式: 无变化，保持兼容性');
  console.log('- 颜色模式: 所有格子向右下偏移6px，修复了向左上的错误偏移');
  console.log('- 1级格子: 在颜色模式基础上额外向左上偏移3px，保持居中效果');
}

// 运行测试
testPositionCalculation();
