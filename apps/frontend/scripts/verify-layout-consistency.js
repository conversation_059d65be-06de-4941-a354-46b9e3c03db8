/**
 * 验证Matrix布局一致性脚本
 * 检查默认模式和颜色模式的容器设定是否一致
 */

const fs = require('fs');
const path = require('path');

// 读取Matrix.tsx文件
const matrixFilePath = path.join(__dirname, '../components/Matrix.tsx');
const matrixContent = fs.readFileSync(matrixFilePath, 'utf8');

console.log('🔍 验证Matrix布局一致性...\n');

// 检查containerPadding是否为固定值
const containerPaddingMatch = matrixContent.match(/const containerPadding = (\d+);/);
if (containerPaddingMatch) {
  const paddingValue = containerPaddingMatch[1];
  console.log(`✅ containerPadding已设为固定值: ${paddingValue}px`);
  
  if (paddingValue === '6') {
    console.log('✅ padding值正确 (6px)');
  } else {
    console.log(`❌ padding值不正确，期望6px，实际${paddingValue}px`);
  }
} else {
  console.log('❌ 未找到containerPadding固定值设定');
}

// 检查是否移除了动态padding逻辑
const dynamicPaddingPattern = /isColorMode\s*\?\s*\d+\s*:\s*0/;
if (!dynamicPaddingPattern.test(matrixContent)) {
  console.log('✅ 已移除动态padding逻辑');
} else {
  console.log('❌ 仍存在动态padding逻辑');
}

// 检查1级格子的特殊样式是否仍然只在颜色模式下生效
const cellSizePattern = /const cellSize = \(isColorMode && isLevel1\) \? 39 : 33;/;
const centerOffsetPattern = /const centerOffset = \(isColorMode && isLevel1\) \? -3 : 0;/;
const borderRadiusPattern = /const borderRadius = isEnhanced \? '50%' : '4px';/;

if (cellSizePattern.test(matrixContent)) {
  console.log('✅ 1级格子大小逻辑正确 (仅颜色模式下放大)');
} else {
  console.log('❌ 1级格子大小逻辑有问题');
}

if (centerOffsetPattern.test(matrixContent)) {
  console.log('✅ 1级格子偏移逻辑正确 (仅颜色模式下偏移)');
} else {
  console.log('❌ 1级格子偏移逻辑有问题');
}

if (borderRadiusPattern.test(matrixContent)) {
  console.log('✅ 1级格子圆角逻辑正确 (仅颜色模式下圆形)');
} else {
  console.log('❌ 1级格子圆角逻辑有问题');
}

// 检查注释是否已更新
const unifiedCommentPattern = /统一矩阵容器尺寸|统一容器padding|统一内边距/;
if (unifiedCommentPattern.test(matrixContent)) {
  console.log('✅ 注释已更新为统一化描述');
} else {
  console.log('❌ 注释未更新');
}

// 检查容器样式设定
const containerStylePattern = /padding: `\$\{containerPadding\}px`/;
if (containerStylePattern.test(matrixContent)) {
  console.log('✅ 容器样式正确使用containerPadding变量');
} else {
  console.log('❌ 容器样式设定有问题');
}

// 检查单元格位置计算
const positionPattern = /left: `\$\{x \* 34 \+ centerOffset \+ containerPadding\}px`/;
if (positionPattern.test(matrixContent)) {
  console.log('✅ 单元格位置计算正确包含containerPadding');
} else {
  console.log('❌ 单元格位置计算有问题');
}

console.log('\n📊 验证总结:');
console.log('- 容器设定已统一化，默认模式和颜色模式使用相同的容器配置');
console.log('- 1级格子的特殊样式仍然只在颜色模式下生效');
console.log('- 避免了模式切换时的布局跳动');
console.log('- 保持了视觉一致性');

console.log('\n🎯 修改效果:');
console.log('- 默认模式: 容器padding=6px, 所有格子33px, 4px圆角');
console.log('- 颜色模式: 容器padding=6px, 1级格子39px+圆形, 其他格子33px+4px圆角');
console.log('- 两种模式的容器设定完全一致 ✅');
