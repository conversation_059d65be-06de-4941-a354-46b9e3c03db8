/**
 * 验证Matrix居中修复效果脚本
 * 检查默认模式和颜色模式的max-width/max-height设置是否一致
 */

const fs = require('fs');
const path = require('path');

// 读取Matrix.tsx文件
const matrixFilePath = path.join(__dirname, '../components/Matrix.tsx');
const matrixContent = fs.readFileSync(matrixFilePath, 'utf8');

console.log('🎯 验证Matrix居中修复效果...\n');

// 检查是否移除了条件性的maxWidth/maxHeight设置
const conditionalMaxWidthPattern = /isColorMode\s*&&\s*\{[^}]*maxWidth[^}]*\}/;
if (!conditionalMaxWidthPattern.test(matrixContent)) {
  console.log('✅ 已移除条件性的maxWidth/maxHeight设置');
} else {
  console.log('❌ 仍存在条件性的maxWidth/maxHeight设置');
}

// 检查是否在...style之后强制设置了maxWidth/maxHeight
const styleSpreadPattern = /\.\.\.style,[\s\S]*?maxWidth:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`[\s\S]*?maxHeight:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`/;
if (styleSpreadPattern.test(matrixContent)) {
  console.log('✅ 在...style之后正确强制设置了maxWidth/maxHeight');
} else {
  console.log('❌ 未在...style之后正确设置maxWidth/maxHeight');
}

// 检查注释是否更新为"所有模式"
const allModesCommentPattern = /所有模式都强制使用正确的尺寸/;
if (allModesCommentPattern.test(matrixContent)) {
  console.log('✅ 注释已更新为"所有模式都强制使用正确的尺寸"');
} else {
  console.log('❌ 注释未更新');
}

// 检查是否移除了重复的maxWidth/maxHeight定义
const duplicateMaxWidthPattern = /maxWidth:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`[\s\S]*?maxWidth:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`/;
if (!duplicateMaxWidthPattern.test(matrixContent)) {
  console.log('✅ 已移除重复的maxWidth/maxHeight定义');
} else {
  console.log('❌ 仍存在重复的maxWidth/maxHeight定义');
}

// 验证viewportStyle的结构
const viewportStyleMatch = matrixContent.match(/const viewportStyle[^}]+\}/s);
if (viewportStyleMatch) {
  const viewportStyleContent = viewportStyleMatch[0];
  
  // 检查是否包含必要的属性
  const hasWidth = /width:\s*['"]100%['"]/.test(viewportStyleContent);
  const hasHeight = /height:\s*['"]100%['"]/.test(viewportStyleContent);
  const hasAspectRatio = /aspectRatio:\s*['"]1\s*\/\s*1['"]/.test(viewportStyleContent);
  const hasStyleSpread = /\.\.\.style/.test(viewportStyleContent);
  const hasFinalMaxWidth = /maxWidth:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`/.test(viewportStyleContent);
  const hasFinalMaxHeight = /maxHeight:\s*`\$\{MATRIX_ACTUAL_SIZE\}px`/.test(viewportStyleContent);
  
  console.log('\n📊 viewportStyle结构验证:');
  console.log(`width: 100% - ${hasWidth ? '✅' : '❌'}`);
  console.log(`height: 100% - ${hasHeight ? '✅' : '❌'}`);
  console.log(`aspectRatio: 1/1 - ${hasAspectRatio ? '✅' : '❌'}`);
  console.log(`...style展开 - ${hasStyleSpread ? '✅' : '❌'}`);
  console.log(`最终maxWidth设置 - ${hasFinalMaxWidth ? '✅' : '❌'}`);
  console.log(`最终maxHeight设置 - ${hasFinalMaxHeight ? '✅' : '❌'}`);
  
  if (hasWidth && hasHeight && hasAspectRatio && hasStyleSpread && hasFinalMaxWidth && hasFinalMaxHeight) {
    console.log('\n🎉 viewportStyle结构完全正确！');
  } else {
    console.log('\n❌ viewportStyle结构存在问题');
  }
} else {
  console.log('❌ 未找到viewportStyle定义');
}

console.log('\n🔍 修复效果分析:');
console.log('- 默认模式: 现在会强制使用1134px的maxWidth/maxHeight');
console.log('- 颜色模式: 同样强制使用1134px的maxWidth/maxHeight');
console.log('- 外部传入的响应式样式会被最终的强制设置覆盖');
console.log('- 确保两种模式下的居中效果完全一致');

console.log('\n✨ 预期效果:');
console.log('- 默认模式下矩阵能够正确居中显示');
console.log('- 颜色模式下矩阵继续正确居中显示');
console.log('- 模式切换时无位移或跳动');
console.log('- 在大屏幕下矩阵尺寸固定为1134px，确保居中');
