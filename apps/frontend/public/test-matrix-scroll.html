<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矩阵滚动功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .matrix-demo {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
            width: 400px;
            height: 400px;
            margin: 20px auto;
            overflow: auto;
            position: relative;
        }
        
        .matrix-grid {
            width: 1023px;
            height: 1023px;
            position: relative;
            background: white;
        }
        
        .demo-cell {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
        }
        
        .demo-cell:hover {
            background: #e3f2fd;
            transform: scale(1.05);
            z-index: 10;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .info-card p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 矩阵滚动功能测试</h1>
            <p>测试30px格子尺寸和滚动功能的实现效果</p>
        </div>
        
        <div class="test-section">
            <h2>📊 测试信息</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h4>矩阵规格</h4>
                    <p>大小: 33 × 33</p>
                    <p>总格子: 1089个</p>
                </div>
                <div class="info-card">
                    <h4>格子尺寸</h4>
                    <p>大小: 30px × 30px</p>
                    <p>间距: 1px</p>
                    <p>单元: 31px</p>
                </div>
                <div class="info-card">
                    <h4>矩阵总尺寸</h4>
                    <p>宽度: 1023px</p>
                    <p>高度: 1023px</p>
                </div>
                <div class="info-card">
                    <h4>滚动特性</h4>
                    <p>视口: 400px × 400px</p>
                    <p>滚动: 双向</p>
                    <p>比例: 正方形</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔍 矩阵演示</h2>
            <p>下面是一个缩小版的矩阵演示，展示滚动效果：</p>
            
            <div class="matrix-demo" id="matrixDemo">
                <div class="matrix-grid" id="matrixGrid">
                    <!-- 格子将通过JavaScript生成 -->
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="scrollToCenter()">滚动到中心</button>
                <button class="btn" onclick="scrollToCorner()">滚动到右下角</button>
                <button class="btn" onclick="scrollToOrigin()">滚动到原点</button>
                <button class="btn" onclick="runTests()">运行测试</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div class="test-results" id="testResults">
点击"运行测试"按钮开始测试...
            </div>
        </div>
    </div>

    <script>
        // 生成演示矩阵
        function generateDemoMatrix() {
            const grid = document.getElementById('matrixGrid');
            grid.innerHTML = '';
            
            // 只生成部分格子用于演示（每隔3个格子生成一个）
            for (let y = 0; y < 33; y += 3) {
                for (let x = 0; x < 33; x += 3) {
                    const cell = document.createElement('div');
                    cell.className = 'demo-cell';
                    cell.style.left = `${x * 31}px`;
                    cell.style.top = `${y * 31}px`;
                    cell.textContent = `${x},${y}`;
                    cell.title = `坐标: (${x}, ${y})`;
                    grid.appendChild(cell);
                }
            }
        }
        
        // 滚动控制函数
        function scrollToCenter() {
            const demo = document.getElementById('matrixDemo');
            const centerX = (demo.scrollWidth - demo.clientWidth) / 2;
            const centerY = (demo.scrollHeight - demo.clientHeight) / 2;
            demo.scrollTo({ left: centerX, top: centerY, behavior: 'smooth' });
        }
        
        function scrollToCorner() {
            const demo = document.getElementById('matrixDemo');
            demo.scrollTo({ 
                left: demo.scrollWidth, 
                top: demo.scrollHeight, 
                behavior: 'smooth' 
            });
        }
        
        function scrollToOrigin() {
            const demo = document.getElementById('matrixDemo');
            demo.scrollTo({ left: 0, top: 0, behavior: 'smooth' });
        }
        
        // 测试函数
        function runTests() {
            const results = document.getElementById('testResults');
            results.textContent = '🚀 开始测试...\n\n';
            
            // 测试1: 尺寸计算
            results.textContent += '🧮 测试矩阵尺寸计算...\n';
            const MATRIX_SIZE = 33;
            const CELL_SIZE = 30;
            const CELL_GAP = 1;
            const CELL_UNIT = CELL_SIZE + CELL_GAP;
            const MATRIX_ACTUAL_SIZE = MATRIX_SIZE * CELL_UNIT;
            
            results.textContent += `矩阵大小: ${MATRIX_SIZE} x ${MATRIX_SIZE}\n`;
            results.textContent += `格子尺寸: ${CELL_SIZE}px x ${CELL_SIZE}px\n`;
            results.textContent += `格子间距: ${CELL_GAP}px\n`;
            results.textContent += `单元总尺寸: ${CELL_UNIT}px\n`;
            results.textContent += `矩阵总尺寸: ${MATRIX_ACTUAL_SIZE}px\n`;
            
            if (MATRIX_ACTUAL_SIZE === 1023) {
                results.textContent += '✅ 矩阵尺寸计算正确\n\n';
            } else {
                results.textContent += '❌ 矩阵尺寸计算错误\n\n';
            }
            
            // 测试2: DOM元素
            results.textContent += '🔍 测试DOM元素...\n';
            const demo = document.getElementById('matrixDemo');
            const grid = document.getElementById('matrixGrid');
            const cells = grid.querySelectorAll('.demo-cell');
            
            results.textContent += `演示容器尺寸: ${demo.clientWidth}px x ${demo.clientHeight}px\n`;
            results.textContent += `矩阵网格尺寸: ${grid.offsetWidth}px x ${grid.offsetHeight}px\n`;
            results.textContent += `演示格子数量: ${cells.length}\n`;
            
            if (cells.length > 0) {
                const firstCell = cells[0];
                results.textContent += `格子尺寸: ${firstCell.offsetWidth}px x ${firstCell.offsetHeight}px\n`;
                
                if (firstCell.offsetWidth === 30 && firstCell.offsetHeight === 30) {
                    results.textContent += '✅ 格子尺寸正确\n\n';
                } else {
                    results.textContent += '❌ 格子尺寸错误\n\n';
                }
            }
            
            // 测试3: 滚动功能
            results.textContent += '📜 测试滚动功能...\n';
            results.textContent += `容器可滚动宽度: ${demo.scrollWidth}px\n`;
            results.textContent += `容器可滚动高度: ${demo.scrollHeight}px\n`;
            results.textContent += `容器客户端宽度: ${demo.clientWidth}px\n`;
            results.textContent += `容器客户端高度: ${demo.clientHeight}px\n`;
            
            const isScrollable = demo.scrollWidth > demo.clientWidth || 
                               demo.scrollHeight > demo.clientHeight;
            
            if (isScrollable) {
                results.textContent += '✅ 容器可滚动\n\n';
            } else {
                results.textContent += '❌ 容器不可滚动\n\n';
            }
            
            // 测试4: 响应式
            results.textContent += '📱 测试响应式行为...\n';
            const aspectRatio = demo.clientWidth / demo.clientHeight;
            results.textContent += `容器宽高比: ${aspectRatio.toFixed(2)}\n`;
            
            if (Math.abs(aspectRatio - 1) < 0.1) {
                results.textContent += '✅ 容器保持正方形比例\n\n';
            } else {
                results.textContent += '⚠️ 容器未保持正方形比例\n\n';
            }
            
            results.textContent += '🎉 测试完成！\n';
            results.textContent += '\n💡 提示: 可以使用滚动按钮测试滚动效果';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateDemoMatrix();
        });
    </script>
</body>
</html>
