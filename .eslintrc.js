module.exports = {
  // 解析器
  parser: '@typescript-eslint/parser',

  // 插件
  plugins: ['@typescript-eslint'],

  // 继承Next.js推荐的ESLint配置
  extends: ['next/core-web-vitals', 'plugin:storybook/recommended'],

  // 解析器选项
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },

  // 环境配置
  env: {
    browser: true,
    es2022: true,
    node: true
  },

  // 全局变量
  globals: {
    React: 'readonly',
    JSX: 'readonly'
  },

  // 规则配置
  rules: {
    // TypeScript相关规则
    '@typescript-eslint/no-unused-vars': ['warn', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    '@typescript-eslint/no-explicit-any': 'warn',

    // React相关规则
    'react/jsx-uses-react': 'off', // Next.js 13+ 不需要导入React
    'react/react-in-jsx-scope': 'off', // Next.js 13+ 不需要导入React
    'react/prop-types': 'off', // 使用TypeScript，不需要prop-types
    'react/display-name': 'warn',
    'react/no-unescaped-entities': 'warn',

    // React Hooks规则
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',

    // 代码质量规则
    'no-console': 'warn',
    'no-debugger': 'error',
    'no-unused-vars': 'off', // 使用TypeScript版本
    'prefer-const': 'warn',
    'no-var': 'error',

    // 代码风格规则 - 放宽要求
    'semi': 'off', // 关闭分号要求
    'quotes': 'off', // 关闭引号要求
    'comma-dangle': 'off', // 关闭尾随逗号要求
    'object-curly-spacing': 'off',
    'array-bracket-spacing': 'off',

    // Next.js特定规则
    '@next/next/no-img-element': 'warn',
    '@next/next/no-html-link-for-pages': 'error'
  },
  
  // 针对特定文件的规则覆盖
  overrides: [
    {
      // Storybook文件
      files: ['**/*.stories.{ts,tsx}', '**/*.story.{ts,tsx}'],
      rules: {
        'react-hooks/rules-of-hooks': 'off',
        'react-hooks/exhaustive-deps': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off'
      }
    },

    {
      // 配置文件
      files: [
        '*.config.{js,ts,mjs}',
        'next.config.*',
        'tailwind.config.*',
        'postcss.config.*'
      ],
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
        'no-console': 'off'
      }
    },
    {
      // API路由文件
      files: ['**/api/**/*.{ts,tsx}', 'api/**/*.{ts,tsx}'],
      rules: {
        'no-console': 'off' // API路由中允许console.log用于调试
      }
    },
    {
      // 脚本文件
      files: ['scripts/**/*.{js,ts}'],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-var-requires': 'off'
      }
    },
    {
      // 钩子文件命名检查
      files: ['**/hooks/**/*.{ts,tsx}'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'default',
            format: ['camelCase']
          },
          {
            selector: 'function',
            format: ['camelCase'],
            prefix: ['use']
          }
        ]
      }
    },
    {
      // 组件文件命名检查
      files: ['**/components/**/*.{tsx}'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'function',
            format: ['PascalCase']
          }
        ]
      }
    },
    {
      // 存储文件命名检查
      files: ['**/stores/**/*.{ts}'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'variable',
            format: ['camelCase'],
            suffix: ['Store']
          }
        ]
      }
    },
    {
      // 服务文件命名检查
      files: ['**/services/**/*.{ts}'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'class',
            format: ['PascalCase'],
            suffix: ['Service']
          }
        ]
      }
    }
  ],
  
  // 忽略模式
  ignorePatterns: [
    'node_modules/',
    '.next/',
    'out/',
    'dist/',
    'build/',
    'coverage/',
    'storybook-static/',
    '*.min.js',
    'public/',
    '.vercel/',
    'prisma/migrations/',
    'types/api-generated/'
  ],
  
  // 设置
  settings: {
    react: {
      version: 'detect'
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true
      }
    }
  }
};
