{"openapi": "3.0.0", "info": {"title": "Cube1 Group API", "version": "1.0.0", "description": "API for Cube1 Group project - 数据可视化和项目管理平台"}, "servers": [{"url": "/api", "description": "Local development server"}], "paths": {"/health": {"get": {"summary": "健康检查", "description": "检查API和数据库连接状态", "responses": {"200": {"description": "系统健康", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "503": {"description": "服务不可用", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/users": {"get": {"summary": "获取用户列表", "description": "分页获取所有用户", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "用户列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedUserResponse"}}}}}}, "post": {"summary": "创建用户", "description": "创建新用户", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/users/{id}": {"get": {"summary": "获取单个用户", "description": "根据ID获取用户详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "用户详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "404": {"description": "用户不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/projects": {"get": {"summary": "获取项目列表", "description": "分页获取项目列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}}, {"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "项目列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProjectResponse"}}}}}}, "post": {"summary": "创建项目", "description": "创建新项目", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectRequest"}}}}, "responses": {"201": {"description": "项目创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}}}}, "/projects/{projectId}": {"get": {"summary": "获取单个项目", "description": "根据ID获取项目详情", "parameters": [{"name": "projectId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "项目详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}, "404": {"description": "项目不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"summary": "更新项目", "description": "更新项目信息", "parameters": [{"name": "projectId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectRequest"}}}}, "responses": {"200": {"description": "项目更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectResponse"}}}}}}, "delete": {"summary": "删除项目", "description": "删除指定项目", "parameters": [{"name": "projectId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "项目删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "error": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "requestId": {"type": "string"}}, "required": ["success", "timestamp"]}, "PaginationInfo": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "totalPages": {"type": "integer"}}, "required": ["page", "limit", "total", "totalPages"]}, "HealthResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok", "error"]}, "database": {"type": "string", "enum": ["healthy", "unhealthy"]}, "api": {"type": "string", "enum": ["ok"]}, "version": {"type": "string"}}}}}]}, "ErrorResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"success": {"type": "boolean", "enum": [false]}}}]}, "SuccessResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"success": {"type": "boolean", "enum": [true]}}}]}, "CreateUserRequest": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}}}, "UserResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id"]}}}]}, "PaginatedUserResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}]}, "CreateProjectRequest": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name"]}, "UpdateProjectRequest": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}}, "ProjectResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "userId": {"type": "string"}, "data": {"type": "object"}, "version": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "userId"]}}}]}, "PaginatedProjectResponse": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "userId": {"type": "string"}, "data": {"type": "object"}, "version": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationInfo"}}}]}}}}